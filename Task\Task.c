/* ͷ�ļ������� */
#include "Task.h"


/* ���������� */

/* ��־λ���� */   
Param_InitTypeDef Param;//��������ṹ�����
Flag_InitTypeDef Flag;  //С��״̬�ṹ�����

/* ���ݱ��� */
uint16_t stop_time_cnt;//Ѳ��ֹͣʱ��
uint16_t back_time_cnt;//����ֹͣʱ��
uint32_t Calibration;  //
uint16_t time;         //Ѳ��ʱ���趨ר�ñ���
uint16_t Back_time;    //����ֹͣʱ��ר�ñ���

/* Task���庯�� */

/*
 * ��������LocationRing_Out
 * ������λ�û����������
 * ���룺��
 * ������ٶȻ�������ֵ
 */
float LocationRing_Out(void)
{
	float ExpectVelocity = 0.0;//�����ٶ�
	
	PID.Location_Actual_Val = Param.Sigma_Motor1Pluse;//���ۼ����������ݸ�λ�û���ʵ��ֵADC1
	
	ExpectVelocity = LocationRing_PID_Realize(PID.Location_Actual_Val);//ͨ��λ�û��õ��������ٶ�
	
	ExpectVelocity = (ExpectVelocity>MOTOR_SPEED_MAX)?MOTOR_SPEED_MAX:(ExpectVelocity<-MOTOR_SPEED_MAX)?(-MOTOR_SPEED_MAX):ExpectVelocity;//���е��ٶ��޷�
	
	return ExpectVelocity;
}

/**
 * ��������VelocityRing_Out
 * �������ٶȻ����������
 * ���룺��
 * �����PWM
 */
float VelocityRing_Out(void)
{
	float EcpectPWM = 0.0;
	
	PID.Velocity_Actual_Val = (Param.UnitTime_Motor1Pluse/(TOTAL_MOTOR1_RESOLUTION*PID_COMPUTATION_PERIOD))*1000*60;//����ȡ���ĵ�λʱ���ڵ���������λתΪRPM
	
	EcpectPWM = VelocityRing_PID_Realize(PID.Velocity_Actual_Val);//�õ������ٶ�
	
	return EcpectPWM;
}

/**
 * ��������LocationRing_VelocityRing_Control
 * ����������PID�Ŀ���
 * ���룺��
 * �������
 */
void LocationRing_VelocityRing_Control(void)
{
	PID.Velocity_Target_Val = LocationRing_Out();
	
	PID.Velocity_Out = VelocityRing_Out();
}

/**
 * ��������Car_Tracking
 * ������ֱ��Ѳ��
 * ���룺��
 * �������
 */
void Car_Tracking(uint16_t TargetDistance)
{
	Flag.Stop_Car = 0;
	Flag.Start_Line = 1;
	Flag.Start_Back = 0;
	Flag.Success_Spin = 0;
	Param.UnitTime_Motor1Pluse= 0;
	Param.UnitTime_Motor2Pluse= 0;
	Param.Sigma_Motor1Pluse= 0;
	Param.Sigma_Motor2Pluse= 0;
	
	
	//1.��Ŀ����븳ֵ��Ŀ�������ֵ
	Param.Distance_TargetThreshold = TargetDistance;
	
	//2.��Ŀ�����ת��Ϊ������ ����/�ܳ��õ���Ҫת��Ȧ��*һȦ�����������,�õ���������������
	PID.Location_Target_Val = ((TargetDistance/(2*3.14*WHEEL_R))*TOTAL_MOTOR1_RESOLUTION);
	
	motor_set_dir(1,FORWARD);
	motor_set_dir(2,FORWARD);
	
	//3.ʹ�ܵ��
	set_motor_enable();
}

/**
 * ��������Car_Back
 * ����������Ѳ��
 * ���룺��
 * �������
 */
void Car_Back(uint16_t TargetDistance)
{
	Flag.Stop_Car = 0;
	Flag.Start_Line = 1;
	Flag.Start_Back = 1;
	Flag.Success_Spin = 0;
	Param.UnitTime_Motor1Pluse= 0;
	Param.UnitTime_Motor2Pluse= 0;
	Param.Sigma_Motor1Pluse= 0;
	Param.Sigma_Motor2Pluse= 0;
	
	
	//1.��Ŀ����븳ֵ��Ŀ�������ֵ
	Param.Distance_TargetThreshold = -TargetDistance;
	
	//2.��Ŀ�����ת��Ϊ������ ����/�ܳ��õ���Ҫת��Ȧ��*һȦ�����������,�õ���������������
	PID.Location_Target_Val = -((TargetDistance/(2*3.14*WHEEL_R))*TOTAL_MOTOR1_RESOLUTION);
	
	motor_set_dir(1,BACK);
	motor_set_dir(2,BACK);
	
	//3.ʹ�ܵ��
	set_motor_enable();
}