/* 头文件包含区 */
#include "Task.h"


/* 参数定义区 */

/* 标志位定义区 */   
Param_InitTypeDef Param;//参数结构体变量
Flag_InitTypeDef Flag;  //小车状态结构体变量

/* 数据变量 */
uint16_t stop_time_cnt;//巡线停止时间
uint16_t back_time_cnt;//后退停止时间
uint32_t Calibration;  //
uint16_t time;         //巡线时间设定专用变量
uint16_t Back_time;    //后退停止时间专用变量

/* Task相关函数 */

/*
 * 函数名：LocationRing_Out
 * 功能：位置环输出函数
 * 输入：无
 * 输出：速度环输入值
 */
float LocationRing_Out(void)
{
	float ExpectVelocity = 0.0;//期望速度
	
	PID.Location_Actual_Val = Param.Sigma_Motor1Pluse;//将累计脉冲数据给位置环实际值ADC1
	
	ExpectVelocity = LocationRing_PID_Realize(PID.Location_Actual_Val);//通过位置环得到期望速度
	
	ExpectVelocity = (ExpectVelocity>MOTOR_SPEED_MAX)?MOTOR_SPEED_MAX:(ExpectVelocity<-MOTOR_SPEED_MAX)?(-MOTOR_SPEED_MAX):ExpectVelocity;//限制电机速度幅值
	
	return ExpectVelocity;
}

/**
 * 函数名：VelocityRing_Out
 * 功能：速度环输出函数
 * 输入：无
 * 输出：PWM
 */
float VelocityRing_Out(void)
{
	float EcpectPWM = 0.0;
	
	PID.Velocity_Actual_Val = (Param.UnitTime_Motor1Pluse/(TOTAL_MOTOR1_RESOLUTION*PID_COMPUTATION_PERIOD))*1000*60;//将获取到的单位时间内的脉冲数单位转为RPM
	
	EcpectPWM = VelocityRing_PID_Realize(PID.Velocity_Actual_Val);//得到期望速度
	
	return EcpectPWM;
}

/**
 * 函数名：LocationRing_VelocityRing_Control
 * 功能：双环PID的控制
 * 输入：无
 * 输出：无
 */
void LocationRing_VelocityRing_Control(void)
{
	PID.Velocity_Target_Val = LocationRing_Out();
	
	PID.Velocity_Out = VelocityRing_Out();
}

/**
 * 函数名：Car_Tracking
 * 功能：直线巡线
 * 输入：无
 * 输出：无
 */
void Car_Tracking(uint16_t TargetDistance)
{
	Flag.Stop_Car = 0;
	Flag.Start_Line = 1;
	Flag.Start_Back = 0;
	Flag.Success_Spin = 0;
	Param.UnitTime_Motor1Pluse= 0;
	Param.UnitTime_Motor2Pluse= 0;
	Param.Sigma_Motor1Pluse= 0;
	Param.Sigma_Motor2Pluse= 0;
	
	
	//1.将目标距离赋值给目标距离阈值
	Param.Distance_TargetThreshold = TargetDistance;
	
	//2.将目标距离转换为脉冲数 距离/周长得到需要转的圈数*一圈对应的脉冲数,得到电机需要的脉冲数
	PID.Location_Target_Val = ((TargetDistance/(2*3.14*WHEEL_R))*TOTAL_MOTOR1_RESOLUTION);
	
	motor_set_dir(1,FORWARD);
	motor_set_dir(2,FORWARD);
	
	//3.使能电机
	set_motor_enable();
}

/**
 * 函数名：Car_Back
 * 功能：后退巡线
 * 输入：无
 * 输出：无
 */
void Car_Back(uint16_t TargetDistance)
{
	Flag.Stop_Car = 0;
	Flag.Start_Line = 0;
	Flag.Start_Back = 1;
	Flag.Success_Spin = 0;
	Param.UnitTime_Motor1Pluse= 0;
	Param.UnitTime_Motor2Pluse= 0;
	Param.Sigma_Motor1Pluse= 0;
	Param.Sigma_Motor2Pluse= 0;
	
	
	//1.将目标距离赋值给目标距离阈值
	Param.Distance_TargetThreshold = TargetDistance;
	
	//2.将目标距离转换为脉冲数 距离/周长得到需要转的圈数*一圈对应的脉冲数,得到电机需要的脉冲数
	PID.Location_Target_Val = ((TargetDistance/(2*3.14*WHEEL_R))*TOTAL_MOTOR1_RESOLUTION);
	
	motor_set_dir(1,BACK);
	motor_set_dir(2,BACK);
	
	//3.使能电机
	set_motor_enable();
}
