#ifndef _TASK_H 
#define _TASK_H 

/* ͷ�ļ������� */
#include "stm32f4xx.h"                  // Device header
#include "sys.h" 
#include <stdio.h>
#include <stdarg.h>
#include <Serial.h>
#include <Delay.h>
#include "string.h"

/* ͷ�ļ������� */
#include <OLED.h>
//#include "usart2.h"
//#include <JY901S.h>
#include "PID.h"
//#include "encoder.h"
#include "BaseTimX.h"
#include "delay.h"
//#include "Relay.h"
//#include "time9pwm.h"
#include "htimx.h"
//#include "Huidu.h"
#include "encoder.h"
#include "tb6612.h"

/* �Զ������������ */
/*
 * Param_InitTypeDef ���ڱ������˶���صĲ���
 */
typedef struct
{
	long 	Sigma_Motor1Pluse;//�ۼ�����������������������ӳ��ʻ���룩
	long 	Sigma_Motor2Pluse;//�ۼ�����������������������ӳ��ʻ���룩
	int16_t UnitTime_Motor1Pluse;//��λʱ���ڵ�����������ӳ�ٶȣ�
	int16_t UnitTime_Motor2Pluse;//��λʱ���ڵ�����������ӳ�ٶȣ�
	float 	Distance_Motor1Curret;//��ǰ��ʻ����
	float 	Distance_Motor2Curret;//��ǰ��ʻ����
	float	Distance_TargetThreshold;//Ŀ�������ֵ
	uint16_t Motor1_PWM;//����PWM���
	uint16_t Motor2_PWM;//�ҵ��PWM���
	uint8_t  Send_Step;
	uint8_t  Back_Step;
	int16_t Line_TempOut;//Ѳ�߲������
	
}Param_InitTypeDef;

/*
 * Flag_InitTypeDef ���ڱ���������б�־λ
 */
typedef struct
{
	uint8_t Is_EnMOTOR;//���ʹ��
	uint8_t Start_Line;//��ʼѲ��
	uint8_t Stop_Car;//ͣ��
	uint8_t Run_Step;
	uint8_t Start_Back;//��ʼ����
	uint8_t Success_Spin;//�����ɹ�
	uint8_t Receive_TargerNum;
	uint8_t Receive_Num_Orientation;
	uint8_t Recognition_Spin_Left;
	uint8_t Pause_Line;//��ͣѲ��
	uint8_t Recognition_Pause;
}Flag_InitTypeDef;

/*
 * SpinDIR_Choose ���ڶ�����������״̬
 */
typedef enum
{
	LEFT_90,//����90��
	RIGHT_90,//����90��
	SPIN_180,//����180��
	Recognition_Spin_Left,
}SpinDIR_Choose;

/* ������������ */

//PID�ļ�������
#define PID_COMPUTATION_PERIOD  10//��λΪms

//��������ת��
#define MOTOR_SPEED_MAX  160 //��λ160RPM,1s���ܵ�������53cm

//����
#define CAR_LENGTH  23
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
/* �ⲿ���������� */

/* ���ڴ�ӡ���� */


/* �ⲿ���ñ��� */
extern Param_InitTypeDef Param;
extern Flag_InitTypeDef Flag;

extern uint16_t time;         //Ѳ��ʱ���趨ר�ñ���
extern uint16_t Back_time;    //����ֹͣʱ��ר�ñ���

extern uint16_t stop_time_cnt;//Ѳ��ֹͣʱ��
extern uint16_t back_time_cnt;//����ֹͣʱ��

/* ���庯�������� */
void Car_Tracking(uint16_t TargetDistance);
void Car_Spin(SpinDIR_Choose Direction);
float LocationRing_Out(void);
float VelocityRing_Out(void);
void LocationRing_VelocityRing_Control(void);
void Car_Back(uint16_t TargetDistance);
#endif  
