#ifndef _TASK_H
#define _TASK_H

/* 头文件包含区 */
#include "stm32f4xx.h"                  // Device header
#include "sys.h"
#include <stdio.h>
#include <stdarg.h>
#include <Serial.h>
#include <Delay.h>
#include "string.h"

/* 头文件包含区 */
#include <OLED.h>
//#include "usart2.h"
//#include <JY901S.h>
#include "PID.h"
//#include "encoder.h"
#include "BaseTimX.h"
#include "delay.h"
//#include "Relay.h"
//#include "time9pwm.h"
#include "htimx.h"
//#include "Huidu.h"
#include "encoder.h"
#include "tb6612.h"

/* 自定义数据类型区 */
/*
 * Param_InitTypeDef 用于保存小车运动相关的参数
 */
typedef struct
{
	long 	Sigma_Motor1Pluse;//累计电机脉冲数（电机累计转动脉冲数）
	long 	Sigma_Motor2Pluse;//累计电机脉冲数（电机累计转动脉冲数）
	int16_t UnitTime_Motor1Pluse;//单位时间内的电机脉冲数（转速）
	int16_t UnitTime_Motor2Pluse;//单位时间内的电机脉冲数（转速）
	float 	Distance_Motor1Curret;//当前行驶距离
	float 	Distance_Motor2Curret;//当前行驶距离
	float	Distance_TargetThreshold;//目标距离阈值
	uint16_t Motor1_PWM;//左电机PWM数值
	uint16_t Motor2_PWM;//右电机PWM数值
	uint8_t  Send_Step;
	uint8_t  Back_Step;
	int16_t Line_TempOut;//巡线参数输出

}Param_InitTypeDef;

/*
 * Flag_InitTypeDef 用于保存小车各种标志位
 */
typedef struct
{
	uint8_t Is_EnMOTOR;//电机使能
	uint8_t Start_Line;//开始巡线
	uint8_t Stop_Car;//停车
	uint8_t Run_Step;
	uint8_t Start_Back;//开始后退
	uint8_t Success_Spin;//旋转成功
	uint8_t Receive_TargerNum;
	uint8_t Receive_Num_Orientation;
	uint8_t Recognition_Spin_Left;
	uint8_t Pause_Line;//暂停巡线
	uint8_t Recognition_Pause;
}Flag_InitTypeDef;

/*
 * SpinDIR_Choose 用于定义旋转方向状态
 */
typedef enum
{
	LEFT_90,//左转90度
	RIGHT_90,//右转90度
	SPIN_180,//旋转180度
	Recognition_Spin_Left,
}SpinDIR_Choose;

/* 宏定义常量区 */

//PID的计算周期
#define PID_COMPUTATION_PERIOD  10//单位为ms

//电机最大转速
#define MOTOR_SPEED_MAX  160 //单位160RPM,1s最多电机转动53cm

//小车长度
#define CAR_LENGTH  23

/* 外部函数声明区 */

/* 用于打印调试 */


/* 外部变量声明 */
extern Param_InitTypeDef Param;
extern Flag_InitTypeDef Flag;

extern uint16_t time;         //巡线时间设定专用变量
extern uint16_t Back_time;    //后退停止时间专用变量

extern uint16_t stop_time_cnt;//巡线停止时间
extern uint16_t back_time_cnt;//后退停止时间

/* 函数声明区 */
void Car_Tracking(uint16_t TargetDistance);
void Car_Spin(SpinDIR_Choose Direction);
float LocationRing_Out(void);
float VelocityRing_Out(void);
void LocationRing_VelocityRing_Control(void);
void Car_Back(uint16_t TargetDistance);
#endif
