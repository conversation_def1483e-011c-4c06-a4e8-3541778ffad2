Component: ARM Compiler 5.06 update 3 (build 300) Tool: armlink [4d35c9]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to _printf_dec.o(.text) for _printf_int_dec
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to noretval__2printf.o(.text) for __2printf
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart2.o(.text) for USART2_Init
    main.o(.text) refers to sys.o(.text) for Set_Nvic_Irq
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to htimx.o(.text) for HTim8_Init
    main.o(.text) refers to tb6612.o(.text) for motor_config_all
    main.o(.text) refers to encoder.o(.text) for encoder_config_all
    main.o(.text) refers to basetimx.o(.text) for BaseTimx_Init
    main.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_Cmd
    main.o(.text) refers to task1.o(.text) for Task_1
    main.o(.text) refers to task.o(.text) for Car_Tracking
    main.o(.text) refers to task.o(.bss) for Param
    main.o(.text) refers to main.o(.data) for task_num
    main.o(.text) refers to basetimx.o(.data) for uwTick
    main.o(.text) refers to htimx.o(.data) for Servo__Angle
    main.o(.text) refers to key.o(.data) for Task_Flag
    main.o(.text) refers to task.o(.data) for time
    main.o(.data) refers to main.o(.text) for test
    main.o(.data) refers to oled.o(.text) for Oled_Proc
    main.o(.data) refers to key.o(.text) for Key_Proc
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    startup_stm32f40_41xxx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to usart2.o(.text) for USART2_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to openmvuart.o(.text) for USART3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to basetimx.o(.text) for TIM6_DAC_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(HEAP) for Heap_Mem
    startup_stm32f40_41xxx.o(.text) refers to startup_stm32f40_41xxx.o(STACK) for Stack_Mem
    stm32f4xx_adc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_cryp.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(.text) refers to stm32f4xx_cryp.o(.text) for CRYP_KeyStructInit
    stm32f4xx_dac.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_fsmc.o(.text) refers to stm32f4xx_fsmc.o(.constdata) for FSMC_DefaultTimingStruct
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_hash_sha1.o(.text) refers to stm32f4xx_hash.o(.text) for HASH_DeInit
    stm32f4xx_i2c.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_ltdc.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for APBAHBPrescTable
    stm32f4xx_rng.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB2PeriphResetCmd
    stm32f4xx_sai.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_wwdg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for fac_us
    sys.o(.text) refers to misc.o(.text) for NVIC_Init
    sys.o(.text) refers to stm32f4xx_usart.o(.text) for USART_SendData
    openmvuart.o(.text) refers to _scanf_int.o(.text) for _scanf_int
    openmvuart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    openmvuart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    openmvuart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    openmvuart.o(.text) refers to sys.o(.text) for Usart_SendByte
    openmvuart.o(.text) refers to __0sscanf.o(.text) for __0sscanf
    openmvuart.o(.text) refers to openmvuart.o(.data) for USART3_Rec_Frame_Flag
    openmvuart.o(.text) refers to openmvuart.o(.bss) for USART3_RX_BUF
    usart2.o(.rev16_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart2.o(.revsh_text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart2.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart2.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    usart2.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    usart2.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    usart2.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart2.o(.text) refers to misc.o(.text) for NVIC_Init
    usart2.o(.text) refers to __0sscanf.o(.text) for __0sscanf
    usart2.o(.text) refers to usart2.o(.bss) for USART2_RX_BUF
    usart2.o(.text) refers to usart2.o(.data) for USART2_Rec_Byte_Length
    usart2.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart2.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    basetimx.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    basetimx.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    basetimx.o(.text) refers to misc.o(.text) for NVIC_Init
    basetimx.o(.text) refers to encoder.o(.text) for encoder_update
    basetimx.o(.text) refers to tb6612.o(.text) for set_motor_disable
    basetimx.o(.text) refers to task.o(.text) for LocationRing_VelocityRing_Control
    basetimx.o(.text) refers to basetimx.o(.data) for uwTick
    basetimx.o(.text) refers to key.o(.data) for Key_Long_Press
    basetimx.o(.text) refers to task.o(.bss) for Flag
    basetimx.o(.text) refers to task.o(.data) for stop_time_cnt
    basetimx.o(.text) refers to pid.o(.bss) for PID
    htimx.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    htimx.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    htimx.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    htimx.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    htimx.o(.text) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    htimx.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    htimx.o(.text) refers to delay.o(.text) for delay_ms
    htimx.o(.text) refers to htimx.o(.data) for Global_Timer8_Arr
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to key.o(.data) for Key_Down
    key.o(.text) refers to basetimx.o(.data) for Time_1s
    key.o(.text) refers to htimx.o(.data) for Servo__Angle
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    oled.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    oled.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    oled.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    oled.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_SetBits
    oled.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    oled.o(.text) refers to delay.o(.text) for delay_ms
    oled.o(.text) refers to oled.o(.constdata) for F8X16
    oled.o(.text) refers to oled.o(.data) for Hzk
    oled.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled.o(.text) refers to noretval__2sprintf.o(.text) for __2sprintf
    oled.o(.text) refers to key.o(.data) for Task_State
    oled.o(.text) refers to basetimx.o(.data) for uwTick
    oled.o(.text) refers to htimx.o(.data) for Servo__Angle
    oled.o(.text) refers to openmvuart.o(.data) for MID_X
    pid.o(.text) refers to pid.o(.bss) for PID
    tb6612.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    tb6612.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    tb6612.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    tb6612.o(.text) refers to task.o(.bss) for Flag
    encoder.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    encoder.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_StructInit
    encoder.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_DeInit
    encoder.o(.text) refers to encoder.o(.data) for encoderDir
    encoder.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    encoder.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    encoder.o(.text) refers to encoder.o(.bss) for encoderSumCnt
    encoder.o(.text) refers to task.o(.bss) for Param
    task.o(.text) refers to pid.o(.text) for LocationRing_PID_Realize
    task.o(.text) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    task.o(.text) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    task.o(.text) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    task.o(.text) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    task.o(.text) refers to tb6612.o(.text) for motor_set_dir
    task.o(.text) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    task.o(.text) refers to task.o(.bss) for Param
    task.o(.text) refers to pid.o(.bss) for PID
    task1.o(.text) refers to task.o(.text) for Car_Tracking
    task1.o(.text) refers to htimx.o(.text) for Servor_Proc
    task1.o(.text) refers to task1.o(.data) for Task_1_Step
    task1.o(.text) refers to task.o(.data) for time
    task1.o(.text) refers to task.o(.bss) for Flag
    task1.o(.text) refers to htimx.o(.data) for Servo__Angle
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart2.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart2.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart2.o(.text) for fputc
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _scanf.o(.text) refers (Weak) to scanf1.o(x$fpl$scanf1) for _scanf_real
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_narrow) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_narrow) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_narrow) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_narrow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__softfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__softfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__softfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__softfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f40_41xxx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart2.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart2.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart2.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.text), (1124 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.text), (2544 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.text), (72 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.text), (856 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.text), (4586 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.text), (472 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.text), (536 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.text), (528 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.text), (100 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.text), (396 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.text), (948 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.text), (936 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.text), (272 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.text), (1684 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.text), (64 bytes).
    Removing stm32f4xx_fsmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fsmc.o(.text), (1480 bytes).
    Removing stm32f4xx_fsmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.text), (552 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.text), (534 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.text), (548 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.text), (1110 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.text), (64 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.text), (1672 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.text), (364 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.text), (160 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.text), (3432 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.text), (524 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.text), (476 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.text), (1152 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (148 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.text), (144 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing openmvuart.o(.rev16_text), (4 bytes).
    Removing openmvuart.o(.revsh_text), (4 bytes).
    Removing usart2.o(.rev16_text), (4 bytes).
    Removing usart2.o(.revsh_text), (4 bytes).
    Removing basetimx.o(.rev16_text), (4 bytes).
    Removing basetimx.o(.revsh_text), (4 bytes).
    Removing htimx.o(.rev16_text), (4 bytes).
    Removing htimx.o(.revsh_text), (4 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing pid.o(.rev16_text), (4 bytes).
    Removing pid.o(.revsh_text), (4 bytes).
    Removing tb6612.o(.rev16_text), (4 bytes).
    Removing tb6612.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing task.o(.rev16_text), (4 bytes).
    Removing task.o(.revsh_text), (4 bytes).
    Removing task1.o(.rev16_text), (4 bytes).
    Removing task1.o(.revsh_text), (4 bytes).

136 unused section(s) (total 27938 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_adc.c             0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_can.c             0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_crc.c             0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp.c            0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_aes.c        0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_des.c        0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_cryp_tdes.c       0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dac.c             0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dbgmcu.c          0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dcmi.c            0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma.c             0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_dma2d.c           0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_exti.c            0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash.c           0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_flash_ramfunc.c   0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_fsmc.c            0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash.c            0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_md5.c        0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_hash_sha1.c       0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_i2c.c             0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_iwdg.c            0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_ltdc.c            0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_pwr.c             0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rng.c             0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rtc.c             0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sai.c             0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_sdio.c            0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_spi.c             0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_wwdg.c            0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\openmv\openmvuart.c            0x00000000   Number         0  openmvuart.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart2\usart2.c                0x00000000   Number         0  usart2.o ABSOLUTE
    ..\Task\Task.c                           0x00000000   Number         0  task.o ABSOLUTE
    ..\Task\Task1.c                          0x00000000   Number         0  task1.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_adc.c          0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_can.c          0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_crc.c          0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp.c         0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_aes.c     0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_des.c     0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_cryp_tdes.c    0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dac.c          0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dbgmcu.c       0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dcmi.c         0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma.c          0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_dma2d.c        0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_exti.c         0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash.c        0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_fsmc.c         0x00000000   Number         0  stm32f4xx_fsmc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash.c         0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_md5.c     0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_hash_sha1.c    0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_i2c.c          0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_iwdg.c         0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_ltdc.c         0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_pwr.c          0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rng.c          0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rtc.c          0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sai.c          0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_sdio.c         0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_spi.c          0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_wwdg.c         0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\openmv\\openmvuart.c         0x00000000   Number         0  openmvuart.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart2\\usart2.c             0x00000000   Number         0  usart2.o ABSOLUTE
    ..\\Task\\Task.c                         0x00000000   Number         0  task.o ABSOLUTE
    ..\\Task\\Task1.c                        0x00000000   Number         0  task1.o ABSOLUTE
    ..\\hardware\\BaseTIMx\\BaseTimX.c       0x00000000   Number         0  basetimx.o ABSOLUTE
    ..\\hardware\\HTIMx\\htimx.c             0x00000000   Number         0  htimx.o ABSOLUTE
    ..\\hardware\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\hardware\\OLEDpin7\\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\hardware\\PID\\PID.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\\hardware\\TB6612\\tb6612.c           0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\\hardware\\encoder\\encoder.c         0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\hardware\\key\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\hardware\BaseTIMx\BaseTimX.c          0x00000000   Number         0  basetimx.o ABSOLUTE
    ..\hardware\HTIMx\htimx.c                0x00000000   Number         0  htimx.o ABSOLUTE
    ..\hardware\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\hardware\OLEDpin7\oled.c              0x00000000   Number         0  oled.o ABSOLUTE
    ..\hardware\PID\PID.c                    0x00000000   Number         0  pid.o ABSOLUTE
    ..\hardware\TB6612\tb6612.c              0x00000000   Number         0  tb6612.o ABSOLUTE
    ..\hardware\encoder\encoder.c            0x00000000   Number         0  encoder.o ABSOLUTE
    ..\hardware\key\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800023c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000242   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000248   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800024c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800024e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000252   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000258   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000258   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000264   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000264   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800026e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800026e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000270   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000272   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000272   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000274   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000274   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000274   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800027a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800027a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800027e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800027e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000286   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000288   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000288   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800028c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000294   Section        0  main.o(.text)
    .text                                    0x0800046c   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x08000488   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x08000489   Thumb Code   220  system_stm32f4xx.o(.text)
    .text                                    0x08000698   Section       64  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08000698   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x080006d8   Section        0  misc.o(.text)
    .text                                    0x080007b8   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08000a4c   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x080010a8   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x08001847   Thumb Code    80  stm32f4xx_tim.o(.text)
    TI3_Config                               0x080018a9   Thumb Code    72  stm32f4xx_tim.o(.text)
    TI2_Config                               0x0800190b   Thumb Code    90  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08001977   Thumb Code    58  stm32f4xx_tim.o(.text)
    .text                                    0x08001d4c   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x080021a0   Section        0  delay.o(.text)
    .text                                    0x080022a4   Section        0  sys.o(.text)
    .text                                    0x08002314   Section        0  openmvuart.o(.text)
    Usart3_SendByte                          0x080023c3   Thumb Code    30  openmvuart.o(.text)
    .text                                    0x0800253c   Section        0  usart2.o(.text)
    .text                                    0x08002834   Section        0  basetimx.o(.text)
    .text                                    0x08002a4c   Section        0  htimx.o(.text)
    .text                                    0x08002d68   Section        0  key.o(.text)
    .text                                    0x08002ed8   Section        0  led.o(.text)
    .text                                    0x08002f64   Section        0  oled.o(.text)
    .text                                    0x0800358c   Section        0  pid.o(.text)
    .text                                    0x08003764   Section        0  tb6612.o(.text)
    .text                                    0x08003d8c   Section        0  encoder.o(.text)
    .text                                    0x08004368   Section        0  task.o(.text)
    .text                                    0x08004578   Section        0  task1.o(.text)
    .text                                    0x08004730   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08004734   Section        0  noretval__2printf.o(.text)
    .text                                    0x0800474c   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08004774   Section        0  _printf_dec.o(.text)
    .text                                    0x080047ec   Section        0  __printf_wp.o(.text)
    .text                                    0x080048fc   Section        0  __0sscanf.o(.text)
    .text                                    0x08004938   Section        0  _scanf_int.o(.text)
    .text                                    0x08004a84   Section        0  heapauxi.o(.text)
    .text                                    0x08004a8a   Section        2  use_no_semi.o(.text)
    .text                                    0x08004a8c   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08004b3e   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08004b41   Thumb Code   428  _printf_fp_dec.o(.text)
    .text                                    0x08004f58   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08004f59   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08004f88   Section        0  _sputc.o(.text)
    .text                                    0x08004f94   Section        0  _printf_char_file.o(.text)
    .text                                    0x08004fb8   Section        0  _chval.o(.text)
    .text                                    0x08004fd4   Section        0  scanf_fp.o(.text)
    _fp_value                                0x08004fd5   Thumb Code   508  scanf_fp.o(.text)
    .text                                    0x08005478   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08005479   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x080054a4   Section        0  _sgetc.o(.text)
    .text                                    0x080054e4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080054ec   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x080054f4   Section      138  lludiv10.o(.text)
    .text                                    0x0800557e   Section        0  isspace.o(.text)
    .text                                    0x08005590   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08005610   Section        0  _scanf.o(.text)
    .text                                    0x08005984   Section        0  bigflt0.o(.text)
    .text                                    0x08005a60   Section        0  ferror.o(.text)
    .text                                    0x08005a68   Section        8  libspace.o(.text)
    .text                                    0x08005a70   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08005abc   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08005acc   Section        0  _rserrno.o(.text)
    .text                                    0x08005ae4   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08005e04   Section        0  scanf_infnan.o(.text)
    .text                                    0x08005f38   Section        0  exit.o(.text)
    .text                                    0x08005f4c   Section      128  strcmpv7m.o(.text)
    .text                                    0x08005fcc   Section       38  llshl.o(.text)
    CL$$btod_d2e                             0x08005ff2   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08006030   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08006076   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080060d6   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x08006410   Section       84  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08006464   Section      198  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x0800652a   Section       40  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08006552   Section       40  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x0800657a   Section       40  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x080065a2   Section       40  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x080065ca   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x0800680e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_ldexp                         0x08006840   Section        0  ldexp.o(i.__hardfp_ldexp)
    i.__mathlib_dbl_overflow                 0x08006910   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08006930   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08006950   Section        0  narrow.o(i.__mathlib_narrow)
    i.__support_ldexp                        0x08006a08   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08006a1c   Section        0  __printf_wp.o(i._is_digit)
    i.frexp                                  0x08006a30   Section        0  frexp.o(i.frexp)
    locale$$code                             0x08006abc   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08006ae8   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$basic                              0x08006b14   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x08006b14   Number         0  basic.o(x$fpl$basic)
    x$fpl$d2f                                0x08006b2c   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08006b2c   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08006b90   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x08006b90   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08006ba1   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08006ce0   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08006ce0   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08006cf0   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08006cf0   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08006d08   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08006d08   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08006d0f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$deqf                               0x08006fb8   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x08006fb8   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dfltu                              0x08007030   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08007030   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08007058   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08007058   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x080070d0   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x080070d0   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08007224   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08007224   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080072c0   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080072c0   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x080072cc   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x080072cc   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$dsub                               0x08007338   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x08007338   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x08007349   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800750c   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x0800750c   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08007562   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08007562   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080075ee   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080075ee   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x080075f8   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x080075f8   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x08007602   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08007602   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x08007606   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08007606   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800766a   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x0800766a   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x080076c6   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x080076c6   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x080076ca   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x080076ca   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x080076d2   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x080076d2   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08007702   Section     2072  oled.o(.constdata)
    x$fpl$usenofp                            0x08007702   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08007f1c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08007f1c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08007f58   Data          64  bigflt0.o(.constdata)
    c$$dinf                                  0x08007fd0   Section        8  fpconst.o(c$$dinf)
    locale$$data                             0x08007fd8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08007fdc   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08007fe4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08007ff0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08007ff2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08007ff3   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x08007ff4   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x08007ff4   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08007ff8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08008000   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08008104   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       37  main.o(.data)
    Scheduler_Task                           0x20000000   Data          36  main.o(.data)
    .data                                    0x20000028   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000003c   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000003c   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x2000004c   Section        4  delay.o(.data)
    fac_us                                   0x2000004c   Data           1  delay.o(.data)
    fac_ms                                   0x2000004e   Data           2  delay.o(.data)
    .data                                    0x20000050   Section       53  openmvuart.o(.data)
    .data                                    0x20000088   Section       20  usart2.o(.data)
    .data                                    0x2000009c   Section        6  basetimx.o(.data)
    .data                                    0x200000a4   Section        6  htimx.o(.data)
    angle_old                                0x200000a9   Data           1  htimx.o(.data)
    .data                                    0x200000aa   Section        7  key.o(.data)
    .data                                    0x200000b1   Section      448  oled.o(.data)
    .data                                    0x20000271   Section        4  encoder.o(.data)
    .data                                    0x20000278   Section       12  task.o(.data)
    .data                                    0x20000284   Section       11  task1.o(.data)
    Task_1_Flag                              0x20000286   Data           1  task1.o(.data)
    Task_1_Step                              0x20000287   Data           8  task1.o(.data)
    .bss                                     0x20000290   Section      200  openmvuart.o(.bss)
    .bss                                     0x20000358   Section      200  usart2.o(.bss)
    .bss                                     0x20000420   Section       72  pid.o(.bss)
    .bss                                     0x20000468   Section       48  encoder.o(.bss)
    .bss                                     0x20000498   Section       43  task.o(.bss)
    .bss                                     0x200004c4   Section       96  libspace.o(.bss)
    HEAP                                     0x20000528   Section      512  startup_stm32f40_41xxx.o(HEAP)
    Heap_Mem                                 0x20000528   Data         512  startup_stm32f40_41xxx.o(HEAP)
    STACK                                    0x20000728   Section     1024  startup_stm32f40_41xxx.o(STACK)
    Stack_Mem                                0x20000728   Data        1024  startup_stm32f40_41xxx.o(STACK)
    __initial_sp                             0x20000b28   Data           0  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_signed                   - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800023d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000243   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x08000249   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800024d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800024f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000259   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000259   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000265   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800026f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000271   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000273   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000275   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000275   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000275   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800027b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800027f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000287   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000289   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800028d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    test                                     0x08000295   Thumb Code    56  main.o(.text)
    Scheduler_Init                           0x080002cd   Thumb Code     8  main.o(.text)
    Scheduler_Run                            0x080002d5   Thumb Code    76  main.o(.text)
    System_Init                              0x08000321   Thumb Code   118  main.o(.text)
    main                                     0x08000397   Thumb Code   152  main.o(.text)
    NMI_Handler                              0x0800046d   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x0800046f   Thumb Code     4  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x08000473   Thumb Code     4  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x08000477   Thumb Code     4  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x0800047b   Thumb Code     4  stm32f4xx_it.o(.text)
    SVC_Handler                              0x0800047f   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x08000481   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x08000483   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x08000485   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000565   Thumb Code    88  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x080005bd   Thumb Code   174  system_stm32f4xx.o(.text)
    Reset_Handler                            0x08000699   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM3_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM5_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART4_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART1_IRQHandler                        0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x080006b3   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    __user_initial_stackheap                 0x080006b5   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x080006d9   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x080006e3   Thumb Code   106  misc.o(.text)
    NVIC_SetVectorTable                      0x0800074d   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x0800075b   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x0800077d   Thumb Code    40  misc.o(.text)
    GPIO_DeInit                              0x080007b9   Thumb Code   268  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x080008c5   Thumb Code   144  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08000955   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000967   Thumb Code    34  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000989   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x0800099b   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x080009a3   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x080009b5   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x080009bd   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x080009c1   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x080009c5   Thumb Code    10  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x080009cf   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x080009d3   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x080009db   Thumb Code    70  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08000a4d   Thumb Code    82  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x08000a9f   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000aad   Thumb Code    60  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000ae9   Thumb Code    56  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000b21   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x08000b35   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x08000b3b   Thumb Code    46  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x08000b69   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08000b6f   Thumb Code    32  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08000b8f   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x08000b95   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x08000ba3   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08000ba9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08000bbd   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08000bc3   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x08000bc9   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08000be5   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000c01   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000c15   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x08000c21   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x08000c35   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x08000c49   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000c5f   Thumb Code   222  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000d3d   Thumb Code    54  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000d73   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08000d7b   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x08000d83   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x08000d89   Thumb Code    26  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x08000da3   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x08000dbf   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x08000dd3   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x08000de7   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x08000dfb   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08000e01   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x08000e23   Thumb Code    78  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08000e71   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x08000e93   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08000eb5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x08000ed7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08000ef9   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x08000f1b   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08000f3d   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x08000f5f   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x08000f81   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08000fa3   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x08000fc5   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08000fe7   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x08001009   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x0800102b   Thumb Code    40  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x08001053   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x08001075   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x08001087   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x0800109d   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x080010a9   Thumb Code   346  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x08001203   Thumb Code   104  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x0800126b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x0800127d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x08001283   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x08001295   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x08001299   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x0800129d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x080012a3   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x080012a9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x080012c1   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x080012d9   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x080012f1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x08001303   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x08001315   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x0800132d   Thumb Code   114  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x0800139f   Thumb Code   154  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08001439   Thumb Code   204  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08001505   Thumb Code   112  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x08001575   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08001589   Thumb Code    86  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x080015df   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x080015e3   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x080015e7   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x080015eb   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x080015ef   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x08001601   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x0800161b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x0800162d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08001647   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08001659   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08001673   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08001685   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x0800169f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x080016b1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x080016cb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x080016dd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x080016f7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08001709   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08001721   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08001733   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x0800174b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x0800175d   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x0800176f   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08001789   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x080017a3   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x080017bd   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x080017d7   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x080017f1   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x0800180f   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x0800182d   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08001897   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x080018f1   Thumb Code    26  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08001965   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x080019b1   Thumb Code   110  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08001a1f   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08001a31   Thumb Code   124  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08001aad   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x08001ab3   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08001ab9   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08001abf   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08001ac5   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08001ae5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08001af7   Thumb Code    30  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08001b15   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08001b2d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08001b45   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08001b57   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08001b5b   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08001b6d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08001b73   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08001b95   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08001b9b   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08001ba5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08001bb7   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08001bcf   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08001bdb   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08001bed   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08001c05   Thumb Code    62  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08001c43   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08001c5f   Thumb Code    54  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08001c95   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08001cb5   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08001cc7   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08001cd9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08001ceb   Thumb Code    66  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08001d2d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08001d45   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08001d4d   Thumb Code   206  stm32f4xx_usart.o(.text)
    USART_Init                               0x08001e1b   Thumb Code   204  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08001ee7   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08001eff   Thumb Code    32  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08001f1f   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x08001f2b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x08001f43   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x08001f53   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x08001f69   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x08001f81   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x08001f89   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x08001f93   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08001fa5   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x08001fbd   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x08001fcf   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x08001fe1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08001ff9   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08002003   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x0800201b   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x0800202b   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x08002043   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x0800205b   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x0800206d   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x08002085   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x08002097   Thumb Code    74  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x080020e1   Thumb Code    26  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x080020fb   Thumb Code    18  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x0800210d   Thumb Code   118  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08002183   Thumb Code    30  stm32f4xx_usart.o(.text)
    delay_init                               0x080021a1   Thumb Code    52  delay.o(.text)
    delay_us                                 0x080021d5   Thumb Code    72  delay.o(.text)
    delay_xms                                0x0800221d   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08002265   Thumb Code    56  delay.o(.text)
    Set_Nvic_Irq                             0x080022a5   Thumb Code    34  sys.o(.text)
    Usart_SendByte                           0x080022c7   Thumb Code    30  sys.o(.text)
    Usart_SendString                         0x080022e5   Thumb Code    46  sys.o(.text)
    Uart3_Init                               0x08002315   Thumb Code   174  openmvuart.o(.text)
    Usart3_SendStr_length                    0x080023e1   Thumb Code    32  openmvuart.o(.text)
    Usart3_SendString                        0x08002401   Thumb Code    28  openmvuart.o(.text)
    Get_Usart3_Data                          0x0800241d   Thumb Code    52  openmvuart.o(.text)
    Clear_Uart3_RecBuf                       0x08002451   Thumb Code    32  openmvuart.o(.text)
    USART3_IRQHandler                        0x08002471   Thumb Code   144  openmvuart.o(.text)
    _sys_exit                                0x0800253d   Thumb Code     6  usart2.o(.text)
    fputc                                    0x08002543   Thumb Code    24  usart2.o(.text)
    USART2_Init                              0x0800255b   Thumb Code   166  usart2.o(.text)
    Serial_SendByte_USART2                   0x08002601   Thumb Code    28  usart2.o(.text)
    Serial_SendArray_USART2                  0x0800261d   Thumb Code    26  usart2.o(.text)
    USART_SendString_USART2                  0x08002637   Thumb Code    26  usart2.o(.text)
    Serial_Pow                               0x08002651   Thumb Code    20  usart2.o(.text)
    Serial_SendNumber_USART2                 0x08002665   Thumb Code    58  usart2.o(.text)
    vofa_To_four                             0x0800269f   Thumb Code    30  usart2.o(.text)
    Send_vofa                                0x080026bd   Thumb Code    56  usart2.o(.text)
    vofa_recvdata                            0x080026f5   Thumb Code    34  usart2.o(.text)
    Clear_Uart2_RecBuf                       0x08002717   Thumb Code    32  usart2.o(.text)
    Get_Usart2_PData                         0x08002737   Thumb Code    36  usart2.o(.text)
    Get_Usart2_IData                         0x0800275b   Thumb Code    36  usart2.o(.text)
    Get_Usart2_DData                         0x0800277f   Thumb Code    36  usart2.o(.text)
    USART2_IRQHandler                        0x080027a3   Thumb Code    98  usart2.o(.text)
    BaseTimx_Init                            0x08002835   Thumb Code    88  basetimx.o(.text)
    TIM6_DAC_IRQHandler                      0x0800288d   Thumb Code   404  basetimx.o(.text)
    HTIM8_GPIO_Config                        0x08002a4d   Thumb Code   100  htimx.o(.text)
    HTim8Mode_Init                           0x08002ab1   Thumb Code   156  htimx.o(.text)
    HTim8_Init                               0x08002b4d   Thumb Code    20  htimx.o(.text)
    HTim8_SetPwmFreq                         0x08002b61   Thumb Code    52  htimx.o(.text)
    HTim8_SetPwmDuty                         0x08002b95   Thumb Code    84  htimx.o(.text)
    ServorCtrlAngle                          0x08002be9   Thumb Code   140  htimx.o(.text)
    ServorCtrlAngleNP                        0x08002c75   Thumb Code   152  htimx.o(.text)
    Servor_Proc                              0x08002d0d   Thumb Code    42  htimx.o(.text)
    KEY_Init                                 0x08002d69   Thumb Code    82  key.o(.text)
    Key_Read                                 0x08002dbb   Thumb Code    50  key.o(.text)
    Key2_Long_Proc                           0x08002ded   Thumb Code    46  key.o(.text)
    Key_Proc                                 0x08002e1b   Thumb Code   136  key.o(.text)
    LED_Init                                 0x08002ed9   Thumb Code   120  led.o(.text)
    OLED_WR_Byte                             0x08002f65   Thumb Code   108  oled.o(.text)
    OLED_Set_Pos                             0x08002fd1   Thumb Code    46  oled.o(.text)
    OLED_Display_On                          0x08002fff   Thumb Code    28  oled.o(.text)
    OLED_Display_Off                         0x0800301b   Thumb Code    28  oled.o(.text)
    OLED_Clear                               0x08003037   Thumb Code    64  oled.o(.text)
    OLED_ShowChar                            0x08003077   Thumb Code   110  oled.o(.text)
    oled_pow                                 0x080030e5   Thumb Code    22  oled.o(.text)
    OLED_ShowNum                             0x080030fb   Thumb Code   132  oled.o(.text)
    OLED_ShowString                          0x0800317f   Thumb Code    54  oled.o(.text)
    OLED_ShowCHinese                         0x080031b5   Thumb Code    98  oled.o(.text)
    OLED_DrawBMP                             0x08003217   Thumb Code   118  oled.o(.text)
    OLED_Init                                0x0800328d   Thumb Code   350  oled.o(.text)
    OLED_ShowFloat                           0x080033eb   Thumb Code   144  oled.o(.text)
    Oled_Proc                                0x0800347b   Thumb Code   184  oled.o(.text)
    PID_Param_Init                           0x0800358d   Thumb Code   144  pid.o(.text)
    LocationRing_PID_Realize                 0x0800361d   Thumb Code    96  pid.o(.text)
    VelocityRing_PID_Realize                 0x0800367d   Thumb Code   212  pid.o(.text)
    HTim1_Init                               0x08003765   Thumb Code   394  tb6612.o(.text)
    Motor1Pwm                                0x080038ef   Thumb Code    60  tb6612.o(.text)
    Motor2Pwm                                0x0800392b   Thumb Code    60  tb6612.o(.text)
    Motor3Pwm                                0x08003967   Thumb Code    60  tb6612.o(.text)
    Motor4Pwm                                0x080039a3   Thumb Code    60  tb6612.o(.text)
    MotorSetPWM                              0x080039df   Thumb Code    62  tb6612.o(.text)
    Motor1ConfigIO                           0x08003a1d   Thumb Code    60  tb6612.o(.text)
    Motor2ConfigIO                           0x08003a59   Thumb Code    60  tb6612.o(.text)
    Motor3ConfigIO                           0x08003a95   Thumb Code    56  tb6612.o(.text)
    Motor4ConfigIO                           0x08003acd   Thumb Code    56  tb6612.o(.text)
    motor_set_dir                            0x08003b05   Thumb Code   280  tb6612.o(.text)
    motor_set_pwm                            0x08003c1d   Thumb Code   104  tb6612.o(.text)
    motor_set_pwmduty                        0x08003c85   Thumb Code   110  tb6612.o(.text)
    motor_config_all                         0x08003cf3   Thumb Code    54  tb6612.o(.text)
    set_motor_enable                         0x08003d29   Thumb Code    36  tb6612.o(.text)
    set_motor_disable                        0x08003d4d   Thumb Code    30  tb6612.o(.text)
    TIM4_MOTOR1_encoder_init                 0x08003d8d   Thumb Code   192  encoder.o(.text)
    TIM5_MOTOR2_encoder_init                 0x08003e4d   Thumb Code   192  encoder.o(.text)
    TIM2_MOTOR3_encoder_init                 0x08003f0d   Thumb Code   252  encoder.o(.text)
    TIM3_MOTOR4_encoder_init                 0x08004009   Thumb Code   192  encoder.o(.text)
    encoder_config_all                       0x080040c9   Thumb Code    20  encoder.o(.text)
    read_encoder                             0x080040dd   Thumb Code   108  encoder.o(.text)
    encoder_get_dir                          0x08004149   Thumb Code   108  encoder.o(.text)
    encoder_get_dir_delta                    0x080041b5   Thumb Code    92  encoder.o(.text)
    encoder_update                           0x08004211   Thumb Code   142  encoder.o(.text)
    calc_motor_rotate_speed                  0x0800429f   Thumb Code   164  encoder.o(.text)
    LocationRing_Out                         0x08004369   Thumb Code   104  task.o(.text)
    VelocityRing_Out                         0x080043d1   Thumb Code    84  task.o(.text)
    LocationRing_VelocityRing_Control        0x08004425   Thumb Code    28  task.o(.text)
    Car_Tracking                             0x08004441   Thumb Code   124  task.o(.text)
    Car_Back                                 0x080044bd   Thumb Code   134  task.o(.text)
    Task_1                                   0x08004579   Thumb Code   414  task1.o(.text)
    __use_no_semihosting                     0x08004731   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08004735   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x0800474d   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_int_dec                          0x08004775   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080047ed   Thumb Code   270  __printf_wp.o(.text)
    __0sscanf                                0x080048fd   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08004939   Thumb Code   332  _scanf_int.o(.text)
    __use_two_region_memory                  0x08004a85   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08004a87   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08004a89   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08004a8b   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08004a8b   Thumb Code     2  use_no_semi.o(.text)
    _printf_int_common                       0x08004a8d   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08004b3f   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08004ced   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08004f63   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08004f89   Thumb Code    10  _sputc.o(.text)
    _printf_char_file                        0x08004f95   Thumb Code    32  _printf_char_file.o(.text)
    _chval                                   0x08004fb9   Thumb Code    28  _chval.o(.text)
    _scanf_really_real                       0x080051d1   Thumb Code   668  scanf_fp.o(.text)
    __vfscanf_char                           0x08005485   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x080054a5   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x080054c3   Thumb Code    34  _sgetc.o(.text)
    __rt_locale                              0x080054e5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x080054ed   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x080054ed   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x080054ed   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x080054f5   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x0800557f   Thumb Code    18  isspace.o(.text)
    _printf_fp_infnan                        0x08005591   Thumb Code   112  _printf_fp_infnan.o(.text)
    __vfscanf                                0x08005611   Thumb Code   878  _scanf.o(.text)
    _btod_etento                             0x08005985   Thumb Code   216  bigflt0.o(.text)
    ferror                                   0x08005a61   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08005a69   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08005a69   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08005a69   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08005a71   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08005abd   Thumb Code    16  rt_ctype_table.o(.text)
    __read_errno                             0x08005acd   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08005ad7   Thumb Code    12  _rserrno.o(.text)
    _scanf_really_hex_real                   0x08005ae5   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08005e05   Thumb Code   292  scanf_infnan.o(.text)
    exit                                     0x08005f39   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08005f4d   Thumb Code   128  strcmpv7m.o(.text)
    __aeabi_llsl                             0x08005fcd   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08005fcd   Thumb Code    38  llshl.o(.text)
    _btod_d2e                                0x08005ff3   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08006031   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08006077   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080060d7   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x08006411   Thumb Code    80  btod.o(CL$$btod_e2d)
    _e2e                                     0x08006465   Thumb Code   198  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x0800652b   Thumb Code    40  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08006553   Thumb Code    40  btod.o(CL$$btod_edivd)
    _btod_emul                               0x0800657b   Thumb Code    40  btod.o(CL$$btod_emul)
    _btod_emuld                              0x080065a3   Thumb Code    40  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x080065cb   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x0800680f   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_ldexp                           0x08006841   Thumb Code   200  ldexp.o(i.__hardfp_ldexp)
    __mathlib_dbl_overflow                   0x08006911   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08006931   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08006951   Thumb Code   166  narrow.o(i.__mathlib_narrow)
    __support_ldexp                          0x08006a09   Thumb Code    20  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08006a1d   Thumb Code    14  __printf_wp.o(i._is_digit)
    frexp                                    0x08006a31   Thumb Code   118  frexp.o(i.frexp)
    _get_lc_numeric                          0x08006abd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08006ae9   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_dneg                             0x08006b15   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x08006b15   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x08006b1b   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x08006b1b   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x08006b21   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x08006b27   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_d2f                              0x08006b2d   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08006b2d   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08006b91   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08006b91   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08006ce1   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08006cf1   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08006d09   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08006d09   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_cdcmpeq                          0x08006fb9   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08006fb9   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_ui2d                             0x08007031   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08007031   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08007059   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08007059   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x080070bb   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x080070d1   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080070d1   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08007225   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080072c1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x080072cd   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x080072cd   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_dsub                             0x08007339   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x08007339   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800750d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800750d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08007563   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080075ef   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080075f7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080075f7   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x080075f9   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x08007603   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x08007607   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800766b   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x080076c7   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x080076cb   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x080076cf   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x080076d3   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    F6x8                                     0x08007702   Data         552  oled.o(.constdata)
    __I$use$fp                               0x08007702   Number         0  usenofp.o(x$fpl$usenofp)
    F8X16                                    0x0800792a   Data        1520  oled.o(.constdata)
    Region$$Table$$Base                      0x08007fb0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007fd0   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x08007fd0   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x08007fd0   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x08007fd0   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x08007fd0   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x08007fd0   Data           0  fpconst.o(c$$dinf)
    __ctype                                  0x08008001   Data           0  lc_ctype_c.o(locale$$data)
    task_num                                 0x20000024   Data           1  main.o(.data)
    SystemCoreClock                          0x20000028   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000002c   Data          16  system_stm32f4xx.o(.data)
    com_data                                 0x20000050   Data           2  openmvuart.o(.data)
    A                                        0x20000054   Data           4  openmvuart.o(.data)
    B                                        0x20000058   Data           4  openmvuart.o(.data)
    C                                        0x2000005c   Data           4  openmvuart.o(.data)
    D                                        0x20000060   Data           4  openmvuart.o(.data)
    E                                        0x20000064   Data           4  openmvuart.o(.data)
    F                                        0x20000068   Data           4  openmvuart.o(.data)
    JL                                       0x2000006c   Data           4  openmvuart.o(.data)
    MID_X                                    0x20000070   Data           4  openmvuart.o(.data)
    MID_Y                                    0x20000074   Data           4  openmvuart.o(.data)
    MID_flag                                 0x20000078   Data           4  openmvuart.o(.data)
    Colour                                   0x2000007c   Data           4  openmvuart.o(.data)
    USART3_RX_STA                            0x20000080   Data           2  openmvuart.o(.data)
    USART3_Rec_Byte_Length                   0x20000082   Data           2  openmvuart.o(.data)
    USART3_Rec_Frame_Flag                    0x20000084   Data           1  openmvuart.o(.data)
    __stdout                                 0x20000088   Data           4  usart2.o(.data)
    USART2_Rec_Byte_Length                   0x2000008c   Data           2  usart2.o(.data)
    USART2_Rec_Frame_Flag                    0x2000008e   Data           1  usart2.o(.data)
    KP                                       0x20000090   Data           4  usart2.o(.data)
    KI                                       0x20000094   Data           4  usart2.o(.data)
    KD                                       0x20000098   Data           4  usart2.o(.data)
    uwTick                                   0x2000009c   Data           4  basetimx.o(.data)
    Time_1s                                  0x200000a0   Data           2  basetimx.o(.data)
    Global_Timer8_Arr                        0x200000a4   Data           4  htimx.o(.data)
    Servo__Angle                             0x200000a8   Data           1  htimx.o(.data)
    Key_Val                                  0x200000aa   Data           1  key.o(.data)
    Key_Down                                 0x200000ab   Data           1  key.o(.data)
    Key_Old                                  0x200000ac   Data           1  key.o(.data)
    Key_Up                                   0x200000ad   Data           1  key.o(.data)
    Task_Flag                                0x200000ae   Data           1  key.o(.data)
    Task_State                               0x200000af   Data           1  key.o(.data)
    Key_Long_Press                           0x200000b0   Data           1  key.o(.data)
    Hzk                                      0x200000b1   Data         448  oled.o(.data)
    encoderDir                               0x20000271   Data           4  encoder.o(.data)
    stop_time_cnt                            0x20000278   Data           2  task.o(.data)
    back_time_cnt                            0x2000027a   Data           2  task.o(.data)
    Calibration                              0x2000027c   Data           4  task.o(.data)
    time                                     0x20000280   Data           2  task.o(.data)
    Back_time                                0x20000282   Data           2  task.o(.data)
    Temp                                     0x20000284   Data           2  task1.o(.data)
    USART3_RX_BUF                            0x20000290   Data         200  openmvuart.o(.bss)
    USART2_RX_BUF                            0x20000358   Data         200  usart2.o(.bss)
    PID                                      0x20000420   Data          72  pid.o(.bss)
    encoderCnt                               0x20000468   Data          16  encoder.o(.bss)
    encoderSumCnt                            0x20000478   Data          16  encoder.o(.bss)
    encoderSumCntOld                         0x20000488   Data          16  encoder.o(.bss)
    Param                                    0x20000498   Data          32  task.o(.bss)
    Flag                                     0x200004b8   Data          11  task.o(.bss)
    __libspace_start                         0x200004c4   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000524   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008394, Max: 0x00100000, ABSOLUTE, COMPRESSED[0x000081e8])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00008104, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          249    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000008   Code   RO         1452  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         1806    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         1804    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         1808    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         1445    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         1444    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000242   0x08000242   0x00000006   Code   RO         1443    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000248   0x08000248   0x00000004   Code   RO         1495    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800024c   0x0800024c   0x00000002   Code   RO         1623    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800024e   0x0800024e   0x00000004   Code   RO         1624    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1627    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1630    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1632    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO         1634    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000006   Code   RO         1635    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000258   0x08000258   0x00000000   Code   RO         1637    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000258   0x08000258   0x0000000c   Code   RO         1638    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         1639    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x00000000   Code   RO         1641    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000264   0x08000264   0x0000000a   Code   RO         1642    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1643    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1645    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1647    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1649    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1651    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1653    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1655    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1657    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1661    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1663    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1665    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000000   Code   RO         1667    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800026e   0x0800026e   0x00000002   Code   RO         1668    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000270   0x08000270   0x00000002   Code   RO         1748    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1759    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1761    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1764    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1767    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1769    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000000   Code   RO         1772    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000272   0x08000272   0x00000002   Code   RO         1773    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000274   0x08000274   0x00000000   Code   RO         1484    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000274   0x08000274   0x00000000   Code   RO         1514    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000274   0x08000274   0x00000006   Code   RO         1526    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800027a   0x0800027a   0x00000000   Code   RO         1516    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800027a   0x0800027a   0x00000004   Code   RO         1517    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000000   Code   RO         1519    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800027e   0x0800027e   0x00000008   Code   RO         1520    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000286   0x08000286   0x00000002   Code   RO         1679    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000288   0x08000288   0x00000000   Code   RO         1711    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000288   0x08000288   0x00000004   Code   RO         1712    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800028c   0x0800028c   0x00000006   Code   RO         1713    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000292   0x08000292   0x00000002   PAD
    0x08000294   0x08000294   0x000001d8   Code   RO            3    .text               main.o
    0x0800046c   0x0800046c   0x0000001a   Code   RO          200    .text               stm32f4xx_it.o
    0x08000486   0x08000486   0x00000002   PAD
    0x08000488   0x08000488   0x00000210   Code   RO          223    .text               system_stm32f4xx.o
    0x08000698   0x08000698   0x00000040   Code   RO          250    .text               startup_stm32f40_41xxx.o
    0x080006d8   0x080006d8   0x000000e0   Code   RO          256    .text               misc.o
    0x080007b8   0x080007b8   0x00000294   Code   RO          631    .text               stm32f4xx_gpio.o
    0x08000a4c   0x08000a4c   0x0000065c   Code   RO          794    .text               stm32f4xx_rcc.o
    0x080010a8   0x080010a8   0x00000ca2   Code   RO          939    .text               stm32f4xx_tim.o
    0x08001d4a   0x08001d4a   0x00000002   PAD
    0x08001d4c   0x08001d4c   0x00000454   Code   RO          979    .text               stm32f4xx_usart.o
    0x080021a0   0x080021a0   0x00000104   Code   RO          999    .text               delay.o
    0x080022a4   0x080022a4   0x0000006e   Code   RO         1022    .text               sys.o
    0x08002312   0x08002312   0x00000002   PAD
    0x08002314   0x08002314   0x00000228   Code   RO         1045    .text               openmvuart.o
    0x0800253c   0x0800253c   0x000002f8   Code   RO         1080    .text               usart2.o
    0x08002834   0x08002834   0x00000218   Code   RO         1112    .text               basetimx.o
    0x08002a4c   0x08002a4c   0x0000031c   Code   RO         1142    .text               htimx.o
    0x08002d68   0x08002d68   0x00000170   Code   RO         1168    .text               key.o
    0x08002ed8   0x08002ed8   0x0000008c   Code   RO         1195    .text               led.o
    0x08002f64   0x08002f64   0x00000628   Code   RO         1215    .text               oled.o
    0x0800358c   0x0800358c   0x000001d8   Code   RO         1254    .text               pid.o
    0x08003764   0x08003764   0x00000628   Code   RO         1287    .text               tb6612.o
    0x08003d8c   0x08003d8c   0x000005dc   Code   RO         1320    .text               encoder.o
    0x08004368   0x08004368   0x00000210   Code   RO         1351    .text               task.o
    0x08004578   0x08004578   0x000001b8   Code   RO         1378    .text               task1.o
    0x08004730   0x08004730   0x00000002   Code   RO         1409    .text               c_w.l(use_no_semi_2.o)
    0x08004732   0x08004732   0x00000002   PAD
    0x08004734   0x08004734   0x00000018   Code   RO         1415    .text               c_w.l(noretval__2printf.o)
    0x0800474c   0x0800474c   0x00000028   Code   RO         1417    .text               c_w.l(noretval__2sprintf.o)
    0x08004774   0x08004774   0x00000078   Code   RO         1421    .text               c_w.l(_printf_dec.o)
    0x080047ec   0x080047ec   0x0000010e   Code   RO         1431    .text               c_w.l(__printf_wp.o)
    0x080048fa   0x080048fa   0x00000002   PAD
    0x080048fc   0x080048fc   0x0000003c   Code   RO         1446    .text               c_w.l(__0sscanf.o)
    0x08004938   0x08004938   0x0000014c   Code   RO         1448    .text               c_w.l(_scanf_int.o)
    0x08004a84   0x08004a84   0x00000006   Code   RO         1450    .text               c_w.l(heapauxi.o)
    0x08004a8a   0x08004a8a   0x00000002   Code   RO         1482    .text               c_w.l(use_no_semi.o)
    0x08004a8c   0x08004a8c   0x000000b2   Code   RO         1485    .text               c_w.l(_printf_intcommon.o)
    0x08004b3e   0x08004b3e   0x0000041a   Code   RO         1487    .text               c_w.l(_printf_fp_dec.o)
    0x08004f58   0x08004f58   0x00000030   Code   RO         1489    .text               c_w.l(_printf_char_common.o)
    0x08004f88   0x08004f88   0x0000000a   Code   RO         1491    .text               c_w.l(_sputc.o)
    0x08004f92   0x08004f92   0x00000002   PAD
    0x08004f94   0x08004f94   0x00000024   Code   RO         1493    .text               c_w.l(_printf_char_file.o)
    0x08004fb8   0x08004fb8   0x0000001c   Code   RO         1496    .text               c_w.l(_chval.o)
    0x08004fd4   0x08004fd4   0x000004a4   Code   RO         1498    .text               c_w.l(scanf_fp.o)
    0x08005478   0x08005478   0x0000002c   Code   RO         1500    .text               c_w.l(scanf_char.o)
    0x080054a4   0x080054a4   0x00000040   Code   RO         1502    .text               c_w.l(_sgetc.o)
    0x080054e4   0x080054e4   0x00000008   Code   RO         1531    .text               c_w.l(rt_locale_intlibspace.o)
    0x080054ec   0x080054ec   0x00000008   Code   RO         1536    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x080054f4   0x080054f4   0x0000008a   Code   RO         1538    .text               c_w.l(lludiv10.o)
    0x0800557e   0x0800557e   0x00000012   Code   RO         1540    .text               c_w.l(isspace.o)
    0x08005590   0x08005590   0x00000080   Code   RO         1542    .text               c_w.l(_printf_fp_infnan.o)
    0x08005610   0x08005610   0x00000374   Code   RO         1546    .text               c_w.l(_scanf.o)
    0x08005984   0x08005984   0x000000dc   Code   RO         1548    .text               c_w.l(bigflt0.o)
    0x08005a60   0x08005a60   0x00000008   Code   RO         1573    .text               c_w.l(ferror.o)
    0x08005a68   0x08005a68   0x00000008   Code   RO         1601    .text               c_w.l(libspace.o)
    0x08005a70   0x08005a70   0x0000004a   Code   RO         1604    .text               c_w.l(sys_stackheap_outer.o)
    0x08005aba   0x08005aba   0x00000002   PAD
    0x08005abc   0x08005abc   0x00000010   Code   RO         1606    .text               c_w.l(rt_ctype_table.o)
    0x08005acc   0x08005acc   0x00000016   Code   RO         1608    .text               c_w.l(_rserrno.o)
    0x08005ae2   0x08005ae2   0x00000002   PAD
    0x08005ae4   0x08005ae4   0x00000320   Code   RO         1610    .text               c_w.l(scanf_hexfp.o)
    0x08005e04   0x08005e04   0x00000134   Code   RO         1612    .text               c_w.l(scanf_infnan.o)
    0x08005f38   0x08005f38   0x00000012   Code   RO         1614    .text               c_w.l(exit.o)
    0x08005f4a   0x08005f4a   0x00000002   PAD
    0x08005f4c   0x08005f4c   0x00000080   Code   RO         1616    .text               c_w.l(strcmpv7m.o)
    0x08005fcc   0x08005fcc   0x00000026   Code   RO         1681    .text               c_w.l(llshl.o)
    0x08005ff2   0x08005ff2   0x0000003e   Code   RO         1551    CL$$btod_d2e        c_w.l(btod.o)
    0x08006030   0x08006030   0x00000046   Code   RO         1553    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08006076   0x08006076   0x00000060   Code   RO         1552    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080060d6   0x080060d6   0x00000338   Code   RO         1561    CL$$btod_div_common  c_w.l(btod.o)
    0x0800640e   0x0800640e   0x00000002   PAD
    0x08006410   0x08006410   0x00000054   Code   RO         1559    CL$$btod_e2d        c_w.l(btod.o)
    0x08006464   0x08006464   0x000000c6   Code   RO         1558    CL$$btod_e2e        c_w.l(btod.o)
    0x0800652a   0x0800652a   0x00000028   Code   RO         1555    CL$$btod_ediv       c_w.l(btod.o)
    0x08006552   0x08006552   0x00000028   Code   RO         1557    CL$$btod_edivd      c_w.l(btod.o)
    0x0800657a   0x0800657a   0x00000028   Code   RO         1554    CL$$btod_emul       c_w.l(btod.o)
    0x080065a2   0x080065a2   0x00000028   Code   RO         1556    CL$$btod_emuld      c_w.l(btod.o)
    0x080065ca   0x080065ca   0x00000244   Code   RO         1560    CL$$btod_mult_common  c_w.l(btod.o)
    0x0800680e   0x0800680e   0x00000030   Code   RO         1591    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800683e   0x0800683e   0x00000002   PAD
    0x08006840   0x08006840   0x000000d0   Code   RO         1694    i.__hardfp_ldexp    m_wm.l(ldexp.o)
    0x08006910   0x08006910   0x00000020   Code   RO         1727    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08006930   0x08006930   0x00000020   Code   RO         1729    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08006950   0x08006950   0x000000b8   Code   RO         1594    i.__mathlib_narrow  m_wm.l(narrow.o)
    0x08006a08   0x08006a08   0x00000014   Code   RO         1696    i.__support_ldexp   m_wm.l(ldexp.o)
    0x08006a1c   0x08006a1c   0x0000000e   Code   RO         1433    i._is_digit         c_w.l(__printf_wp.o)
    0x08006a2a   0x08006a2a   0x00000006   PAD
    0x08006a30   0x08006a30   0x0000008c   Code   RO         1675    i.frexp             m_wm.l(frexp.o)
    0x08006abc   0x08006abc   0x0000002c   Code   RO         1578    locale$$code        c_w.l(lc_numeric_c.o)
    0x08006ae8   0x08006ae8   0x0000002c   Code   RO         1684    locale$$code        c_w.l(lc_ctype_c.o)
    0x08006b14   0x08006b14   0x00000018   Code   RO         1454    x$fpl$basic         fz_wm.l(basic.o)
    0x08006b2c   0x08006b2c   0x00000062   Code   RO         1456    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08006b8e   0x08006b8e   0x00000002   PAD
    0x08006b90   0x08006b90   0x00000150   Code   RO         1458    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08006ce0   0x08006ce0   0x00000010   Code   RO         1749    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x08006cf0   0x08006cf0   0x00000018   Code   RO         1688    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08006d08   0x08006d08   0x000002b0   Code   RO         1465    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08006fb8   0x08006fb8   0x00000078   Code   RO         1669    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08007030   0x08007030   0x00000026   Code   RO         1468    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08007056   0x08007056   0x00000002   PAD
    0x08007058   0x08007058   0x00000078   Code   RO         1690    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x080070d0   0x080070d0   0x00000154   Code   RO         1474    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08007224   0x08007224   0x0000009c   Code   RO         1504    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x080072c0   0x080072c0   0x0000000c   Code   RO         1506    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080072cc   0x080072cc   0x0000006c   Code   RO         1671    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08007338   0x08007338   0x000001d4   Code   RO         1460    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x0800750c   0x0800750c   0x00000056   Code   RO         1476    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08007562   0x08007562   0x0000008c   Code   RO         1508    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080075ee   0x080075ee   0x0000000a   Code   RO         1692    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080075f8   0x080075f8   0x0000000a   Code   RO         1510    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x08007602   0x08007602   0x00000004   Code   RO         1478    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08007606   0x08007606   0x00000064   Code   RO         1774    x$fpl$retnan        fz_wm.l(retnan.o)
    0x0800766a   0x0800766a   0x0000005c   Code   RO         1721    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x080076c6   0x080076c6   0x00000004   Code   RO         1480    x$fpl$scanf1        fz_wm.l(scanf1.o)
    0x080076ca   0x080076ca   0x00000008   Code   RO         1585    x$fpl$scanf2        fz_wm.l(scanf2.o)
    0x080076d2   0x080076d2   0x00000030   Code   RO         1780    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x08007702   0x08007702   0x00000000   Code   RO         1512    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08007702   0x08007702   0x00000818   Data   RO         1216    .constdata          oled.o
    0x08007f1a   0x08007f1a   0x00000002   PAD
    0x08007f1c   0x08007f1c   0x00000094   Data   RO         1549    .constdata          c_w.l(bigflt0.o)
    0x08007fb0   0x08007fb0   0x00000020   Data   RO         1802    Region$$Table       anon$$obj.o
    0x08007fd0   0x08007fd0   0x00000008   Data   RO         1580    c$$dinf             fz_wm.l(fpconst.o)
    0x08007fd8   0x08007fd8   0x0000001c   Data   RO         1577    locale$$data        c_w.l(lc_numeric_c.o)
    0x08007ff4   0x08007ff4   0x00000110   Data   RO         1683    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08008104, Size: 0x00000b28, Max: 0x00020000, ABSOLUTE, COMPRESSED[0x000000e4])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000025   Data   RW            4    .data               main.o
    0x20000025   COMPRESSED   0x00000003   PAD
    0x20000028   COMPRESSED   0x00000014   Data   RW          224    .data               system_stm32f4xx.o
    0x2000003c   COMPRESSED   0x00000010   Data   RW          795    .data               stm32f4xx_rcc.o
    0x2000004c   COMPRESSED   0x00000004   Data   RW         1000    .data               delay.o
    0x20000050   COMPRESSED   0x00000035   Data   RW         1047    .data               openmvuart.o
    0x20000085   COMPRESSED   0x00000003   PAD
    0x20000088   COMPRESSED   0x00000014   Data   RW         1082    .data               usart2.o
    0x2000009c   COMPRESSED   0x00000006   Data   RW         1113    .data               basetimx.o
    0x200000a2   COMPRESSED   0x00000002   PAD
    0x200000a4   COMPRESSED   0x00000006   Data   RW         1143    .data               htimx.o
    0x200000aa   COMPRESSED   0x00000007   Data   RW         1169    .data               key.o
    0x200000b1   COMPRESSED   0x000001c0   Data   RW         1217    .data               oled.o
    0x20000271   COMPRESSED   0x00000004   Data   RW         1322    .data               encoder.o
    0x20000275   COMPRESSED   0x00000003   PAD
    0x20000278   COMPRESSED   0x0000000c   Data   RW         1353    .data               task.o
    0x20000284   COMPRESSED   0x0000000b   Data   RW         1379    .data               task1.o
    0x2000028f   COMPRESSED   0x00000001   PAD
    0x20000290        -       0x000000c8   Zero   RW         1046    .bss                openmvuart.o
    0x20000358        -       0x000000c8   Zero   RW         1081    .bss                usart2.o
    0x20000420        -       0x00000048   Zero   RW         1255    .bss                pid.o
    0x20000468        -       0x00000030   Zero   RW         1321    .bss                encoder.o
    0x20000498        -       0x0000002b   Zero   RW         1352    .bss                task.o
    0x200004c3   COMPRESSED   0x00000001   PAD
    0x200004c4        -       0x00000060   Zero   RW         1602    .bss                c_w.l(libspace.o)
    0x20000524   COMPRESSED   0x00000004   PAD
    0x20000528        -       0x00000200   Zero   RW          248    HEAP                startup_stm32f40_41xxx.o
    0x20000728        -       0x00000400   Zero   RW          247    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       536         44          0          6          0       1289   basetimx.o
       260          8          0          4          0       1525   delay.o
      1500         62          0          4         48       4530   encoder.o
       796         50          0          6          0       2975   htimx.o
       368         60          0          7          0       1622   key.o
       140         20          0          0          0        587   led.o
       472         68          0         37          0      56832   main.o
       224         20          0          0          0     231049   misc.o
      1576        102       2072        448          0       5950   oled.o
       552         60          0         53        200       3517   openmvuart.o
       472         20          0          0         72       2141   pid.o
        64         26        392          0       1536        848   startup_stm32f40_41xxx.o
       660         44          0          0          0       4201   stm32f4xx_gpio.o
        26          0          0          0          0       1246   stm32f4xx_it.o
      1628         52          0         16          0      13100   stm32f4xx_rcc.o
      3234         60          0          0          0      23060   stm32f4xx_tim.o
      1108         34          0          0          0       7928   stm32f4xx_usart.o
       110          0          0          0          0       1515   sys.o
       528         46          0         20          0       1843   system_stm32f4xx.o
       528         54          0         12         43       3468   task.o
       440         26          0         11          0       1120   task1.o
      1576         72          0          0          0       4427   tb6612.o
       760         48          0         20        200       6816   usart2.o

    ----------------------------------------------------------------------
     17564        <USER>       <GROUP>        656       2100     381589   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         6          0          2         12          1          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1050          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
       884          6          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        10          0          0          0          0         68   _sputc.o
       220          4        148          0          0         96   bigflt0.o
      2074        132          0          0          0        940   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
      1188         12          0          0          0        148   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        24          0          0          0          0        164   basic.o
        98          4          0          0          0        140   d2f.o
       804         16          0          0          0        368   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
       120          4          0          0          0        140   deqf.o
        38          0          0          0          0        116   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
         0          0          8          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         4          0          0          0          0        116   printf1.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        64         16          0          0          0        248   dunder.o
        48          0          0          0          0        124   fpclassify.o
       140         22          0          0          0        132   frexp.o
       228          8          0          0          0        308   ldexp.o
       184         18          0          0          0        152   narrow.o

    ----------------------------------------------------------------------
     12510        <USER>        <GROUP>          0        100       8620   Library Totals
        30          2          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      8766        296        448          0         96       4356   c_w.l
      3050        196          8          0          0       3300   fz_wm.l
       664         64          0          0          0        964   m_wm.l

    ----------------------------------------------------------------------
     12510        <USER>        <GROUP>          0        100       8620   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     30074       1534       2954        656       2200     380765   Grand Totals
     30074       1534       2954        228       2200     380765   ELF Image Totals (compressed)
     30074       1534       2954        228          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                33028 (  32.25kB)
    Total RW  Size (RW Data + ZI Data)              2856 (   2.79kB)
    Total ROM Size (Code + RO Data + RW Data)      33256 (  32.48kB)

==============================================================================

