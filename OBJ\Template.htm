<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [..\OBJ\Template.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image ..\OBJ\Template.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060300: Last Updated: Mon Jul 14 19:18:13 2025
<BR><P>
<H3>Maximum Stack Usage =        536 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
USART3_IRQHandler &rArr; Get_Usart3_Data &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[149]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[22]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[22]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[22]">ADC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[a]">BusFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[24]">CAN1_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[25]">CAN1_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[26]">CAN1_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[23]">CAN1_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[50]">CAN2_RX0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[51]">CAN2_RX1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[52]">CAN2_SCE_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4f]">CAN2_TX_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5f]">CRYP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5e]">DCMI_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[21]">DMA1_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3f]">DMA1_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[55]">DMA2_Stream6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[56]">DMA2_Stream7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[d]">DebugMon_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4d]">ETH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4e]">ETH_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[16]">EXTI0_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[38]">EXTI15_10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[17]">EXTI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[18]">EXTI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[19]">EXTI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[1a]">EXTI4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[27]">EXTI9_5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[14]">FLASH_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[61]">FPU_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[40]">FSMC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[60]">HASH_RNG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[8]">HardFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[30]">I2C1_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2f]">I2C1_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[32]">I2C2_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[31]">I2C2_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[59]">I2C3_ER_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[58]">I2C3_EV_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5]">Key_Proc</a> from key.o(.text) referenced 2 times from main.o(.data)
 <LI><a href="#[9]">MemManage_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[7]">NMI_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[53]">OTG_FS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3a]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5b]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5d]">OTG_HS_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[5c]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[4]">Oled_Proc</a> from oled.o(.text) referenced 2 times from main.o(.data)
 <LI><a href="#[11]">PVD_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[e]">PendSV_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[15]">RCC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[39]">RTC_Alarm_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[13]">RTC_WKUP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6]">Reset_Handler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[41]">SDIO_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[33]">SPI1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[34]">SPI2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[43]">SPI3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[c]">SVC_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[f]">SysTick_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[62]">SystemInit</a> from system_stm32f4xx.o(.text) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[12]">TAMP_STAMP_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[28]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2b]">TIM1_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2a]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[29]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2c]">TIM2_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2d]">TIM3_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[2e]">TIM4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[42]">TIM5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[46]">TIM6_DAC_IRQHandler</a> from basetimx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[47]">TIM7_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3b]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3e]">TIM8_CC_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3d]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[3c]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[44]">UART4_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[45]">UART5_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[35]">USART1_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[36]">USART2_IRQHandler</a> from usart2.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[37]">USART3_IRQHandler</a> from openmvuart.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[57]">USART6_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[b]">UsageFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[10]">WWDG_IRQHandler</a> from startup_stm32f40_41xxx.o(.text) referenced from startup_stm32f40_41xxx.o(RESET)
 <LI><a href="#[6c]">__main</a> from __main.o(!!!main) referenced from startup_stm32f40_41xxx.o(.text)
 <LI><a href="#[6b]">_get_lc_ctype</a> from lc_ctype_c.o(locale$$code) referenced from rt_ctype_table.o(.text)
 <LI><a href="#[67]">_printf_input_char</a> from _printf_char_common.o(.text) referenced from _printf_char_common.o(.text)
 <LI><a href="#[66]">_sbackspace</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[69]">_scanf_char_input</a> from scanf_char.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[65]">_sgetc</a> from _sgetc.o(.text) referenced from __0sscanf.o(.text)
 <LI><a href="#[64]">_sputc</a> from _sputc.o(.text) referenced from noretval__2sprintf.o(.text)
 <LI><a href="#[68]">fputc</a> from usart2.o(.text) referenced from _printf_char_file.o(.text)
 <LI><a href="#[6a]">isspace</a> from isspace.o(.text) referenced from scanf_char.o(.text)
 <LI><a href="#[3]">test</a> from main.o(.text) referenced 2 times from main.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[6c]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[6d]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[6f]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[173]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[174]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[175]"></a>__decompress</STRONG> (Thumb, 90 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[176]"></a>__decompress1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(!!dczerorl2), UNUSED)

<P><STRONG><a name="[177]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[70]"></a>_printf_f</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_f.o(.ARM.Collect$$_printf_percent$$00000003))
<BR><BR>[Stack]<UL><LI>Max Depth = 320 + Unknown Stack Size
<LI>Call Chain = _printf_f &rArr; _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[12f]"></a>_printf_percent</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>

<P><STRONG><a name="[72]"></a>_printf_d</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_d.o(.ARM.Collect$$_printf_percent$$00000009))
<BR><BR>[Stack]<UL><LI>Max Depth = 56 + Unknown Stack Size
<LI>Call Chain = _printf_d &rArr; _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[178]"></a>_printf_percent_end</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017))

<P><STRONG><a name="[7e]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[74]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000001))
<BR><BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_init
</UL>

<P><STRONG><a name="[179]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[76]"></a>__rt_lib_init_lc_common</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000F))
<BR><BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>

<P><STRONG><a name="[17a]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[17b]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[17c]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[17d]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[78]"></a>__rt_lib_init_lc_ctype_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000012))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_ctype_2 &rArr; _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
</UL>

<P><STRONG><a name="[17e]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[17f]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[79]"></a>__rt_lib_init_lc_numeric_2</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000016))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_lib_init_lc_numeric_2 &rArr; _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[180]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[181]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[182]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[183]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[184]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[185]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[186]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[187]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[188]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[189]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[18a]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[18b]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[18c]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[83]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[18d]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[18e]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[18f]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[190]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[191]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[192]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[193]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[6e]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[194]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[7b]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[7d]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[195]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[7f]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 116 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; System_Init &rArr; USART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[196]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[14f]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[82]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[197]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[84]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[3]"></a>test</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = test &rArr; __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(.data)
</UL>
<P><STRONG><a name="[98]"></a>Scheduler_Init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[99]"></a>Scheduler_Run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Scheduler_Run
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>System_Init</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = System_Init &rArr; USART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_config_all
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_config_all
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Nvic_Irq
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_SetPwmFreq
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>main</STRONG> (Thumb, 152 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = main &rArr; System_Init &rArr; USART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_1
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servor_Proc
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Run
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[7]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(.text)
</UL>
<P><STRONG><a name="[198]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, system_stm32f4xx.o(.text), UNUSED)

<P><STRONG><a name="[6]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f40_41xxx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[149]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f40_41xxx.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[8a]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[bc]"></a>NVIC_Init</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Nvic_Irq
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
</UL>

<P><STRONG><a name="[199]"></a>NVIC_SetVectorTable</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, misc.o(.text), UNUSED)

<P><STRONG><a name="[19a]"></a>NVIC_SystemLPConfig</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, misc.o(.text), UNUSED)

<P><STRONG><a name="[b9]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[9f]"></a>GPIO_DeInit</STRONG> (Thumb, 268 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphResetCmd
</UL>

<P><STRONG><a name="[c5]"></a>GPIO_Init</STRONG> (Thumb, 144 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor4ConfigIO
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor3ConfigIO
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2ConfigIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1ConfigIO
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTIM8_GPIO_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[116]"></a>GPIO_StructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[19b]"></a>GPIO_PinLockConfig</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[19c]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[19d]"></a>GPIO_ReadInputData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[19e]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[19f]"></a>GPIO_ReadOutputData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor4ConfigIO
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor3ConfigIO
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2ConfigIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1ConfigIO
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[f9]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[1a0]"></a>GPIO_WriteBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[1a1]"></a>GPIO_Write</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>GPIO_ToggleBits</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text), UNUSED)

<P><STRONG><a name="[c4]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 70 bytes, Stack size 20 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTIM8_GPIO_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[1a3]"></a>RCC_DeInit</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1a4]"></a>RCC_HSEConfig</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[a2]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
</UL>

<P><STRONG><a name="[a1]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>

<P><STRONG><a name="[1a5]"></a>RCC_AdjustHSICalibrationValue</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1a6]"></a>RCC_HSICmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1a7]"></a>RCC_LSEConfig</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1a8]"></a>RCC_LSICmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1a9]"></a>RCC_PLLConfig</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1aa]"></a>RCC_PLLCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ab]"></a>RCC_PLLI2SConfig</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ac]"></a>RCC_PLLI2SCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ad]"></a>RCC_PLLSAIConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ae]"></a>RCC_PLLSAICmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1af]"></a>RCC_ClockSecuritySystemCmd</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b0]"></a>RCC_MCO1Config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b1]"></a>RCC_MCO2Config</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b2]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b3]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b4]"></a>RCC_HCLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b5]"></a>RCC_PCLK1Config</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b6]"></a>RCC_PCLK2Config</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[b8]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 222 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[1b7]"></a>RCC_RTCCLKConfig</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b8]"></a>RCC_RTCCLKCmd</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1b9]"></a>RCC_BackupResetCmd</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ba]"></a>RCC_I2SCLKConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1bb]"></a>RCC_SAIPLLI2SClkDivConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1bc]"></a>RCC_SAIPLLSAIClkDivConfig</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1bd]"></a>RCC_SAIBlockACLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1be]"></a>RCC_SAIBlockBCLKConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1bf]"></a>RCC_LTDCCLKDivConfig</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c0]"></a>RCC_TIMCLKPresConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[c2]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor4ConfigIO
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor3ConfigIO
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2ConfigIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1ConfigIO
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTIM8_GPIO_Config
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[1c1]"></a>RCC_AHB2PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c2]"></a>RCC_AHB3PeriphClockCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[c3]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[e8]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[a0]"></a>RCC_AHB1PeriphResetCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
</UL>

<P><STRONG><a name="[1c3]"></a>RCC_AHB2PeriphResetCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c4]"></a>RCC_AHB3PeriphResetCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[a5]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>

<P><STRONG><a name="[a4]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>

<P><STRONG><a name="[1c5]"></a>RCC_AHB1PeriphClockLPModeCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c6]"></a>RCC_AHB2PeriphClockLPModeCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c7]"></a>RCC_AHB3PeriphClockLPModeCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c8]"></a>RCC_APB1PeriphClockLPModeCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1c9]"></a>RCC_APB2PeriphClockLPModeCmd</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ca]"></a>RCC_LSEModeConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1cb]"></a>RCC_ITConfig</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1cc]"></a>RCC_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1cd]"></a>RCC_GetITStatus</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[1ce]"></a>RCC_ClearITPendingBit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[a3]"></a>TIM_DeInit</STRONG> (Thumb, 346 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[dd]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 104 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_SetPwmFreq
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[117]"></a>TIM_TimeBaseStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[1cf]"></a>TIM_PrescalerConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d0]"></a>TIM_CounterModeConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[11a]"></a>TIM_SetCounter</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_encoder
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[1d1]"></a>TIM_SetAutoreload</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d2]"></a>TIM_GetCounter</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d3]"></a>TIM_GetPrescaler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d4]"></a>TIM_UpdateDisableConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d5]"></a>TIM_UpdateRequestConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d6]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d7]"></a>TIM_SelectOnePulseMode</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1d8]"></a>TIM_SetClockDivision</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[95]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_config_all
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_Init
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwmduty
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwm
</UL>

<P><STRONG><a name="[e9]"></a>TIM_OC1Init</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC1Init
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[eb]"></a>TIM_OC2Init</STRONG> (Thumb, 154 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC2Init
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[106]"></a>TIM_OC3Init</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
</UL>

<P><STRONG><a name="[108]"></a>TIM_OC4Init</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
</UL>

<P><STRONG><a name="[1d9]"></a>TIM_OCStructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1da]"></a>TIM_SelectOCxM</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[f3]"></a>TIM_SetCompare1</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor3Pwm
</UL>

<P><STRONG><a name="[f4]"></a>TIM_SetCompare2</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor4Pwm
</UL>

<P><STRONG><a name="[ef]"></a>TIM_SetCompare3</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1Pwm
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngleNP
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_SetPwmDuty
</UL>

<P><STRONG><a name="[f0]"></a>TIM_SetCompare4</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2Pwm
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngleNP
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_SetPwmDuty
</UL>

<P><STRONG><a name="[1db]"></a>TIM_ForcedOC1Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1dc]"></a>TIM_ForcedOC2Config</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1dd]"></a>TIM_ForcedOC3Config</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1de]"></a>TIM_ForcedOC4Config</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[ea]"></a>TIM_OC1PreloadConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[ec]"></a>TIM_OC2PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
</UL>

<P><STRONG><a name="[107]"></a>TIM_OC3PreloadConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
</UL>

<P><STRONG><a name="[109]"></a>TIM_OC4PreloadConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
</UL>

<P><STRONG><a name="[1df]"></a>TIM_OC1FastConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e0]"></a>TIM_OC2FastConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e1]"></a>TIM_OC3FastConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e2]"></a>TIM_OC4FastConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e3]"></a>TIM_ClearOC1Ref</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e4]"></a>TIM_ClearOC2Ref</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e5]"></a>TIM_ClearOC3Ref</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e6]"></a>TIM_ClearOC4Ref</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e7]"></a>TIM_OC1PolarityConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e8]"></a>TIM_OC1NPolarityConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1e9]"></a>TIM_OC2PolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ea]"></a>TIM_OC2NPolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1eb]"></a>TIM_OC3PolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ec]"></a>TIM_OC3NPolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ed]"></a>TIM_OC4PolarityConfig</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ee]"></a>TIM_CCxCmd</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ef]"></a>TIM_CCxNCmd</STRONG> (Thumb, 30 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[ae]"></a>TIM_SetIC4Prescaler</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[ac]"></a>TIM_SetIC3Prescaler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[aa]"></a>TIM_SetIC2Prescaler</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[a8]"></a>TIM_SetIC1Prescaler</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[a6]"></a>TIM_ICInit</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI3_Config
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI4_Config
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC3Prescaler
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC4Prescaler
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[119]"></a>TIM_ICStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[af]"></a>TIM_PWMIConfig</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[1f0]"></a>TIM_GetCapture1</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f1]"></a>TIM_GetCapture2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f2]"></a>TIM_GetCapture3</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f3]"></a>TIM_GetCapture4</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f4]"></a>TIM_BDTRConfig</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f5]"></a>TIM_BDTRStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[ed]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwmduty
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwm
</UL>

<P><STRONG><a name="[1f6]"></a>TIM_SelectCOM</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f7]"></a>TIM_CCPreloadControl</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[df]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[1f8]"></a>TIM_GenerateEvent</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1f9]"></a>TIM_GetFlagStatus</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[de]"></a>TIM_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BaseTimx_Init
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[e0]"></a>TIM_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[e5]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[1fa]"></a>TIM_DMAConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1fb]"></a>TIM_DMACmd</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1fc]"></a>TIM_SelectCCDMA</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1fd]"></a>TIM_InternalClockConfig</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[b1]"></a>TIM_SelectInputTrigger</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRxExternalClockConfig
</UL>

<P><STRONG><a name="[b0]"></a>TIM_ITRxExternalClockConfig</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
</UL>

<P><STRONG><a name="[b2]"></a>TIM_TIxExternalClockConfig</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[b4]"></a>TIM_ETRConfig</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode2Config
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode1Config
</UL>

<P><STRONG><a name="[b3]"></a>TIM_ETRClockMode1Config</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[b5]"></a>TIM_ETRClockMode2Config</STRONG> (Thumb, 32 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[1fe]"></a>TIM_SelectOutputTrigger</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ff]"></a>TIM_SelectSlaveMode</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[200]"></a>TIM_SelectMasterSlaveMode</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[118]"></a>TIM_EncoderInterfaceConfig</STRONG> (Thumb, 66 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_EncoderInterfaceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>

<P><STRONG><a name="[201]"></a>TIM_SelectHallSensor</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[202]"></a>TIM_RemapConfig</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[b6]"></a>USART_DeInit</STRONG> (Thumb, 206 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>

<P><STRONG><a name="[b7]"></a>USART_Init</STRONG> (Thumb, 204 bytes, Stack size 48 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[203]"></a>USART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[204]"></a>USART_ClockInit</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[205]"></a>USART_ClockStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c7]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[206]"></a>USART_SetPrescaler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[207]"></a>USART_OverSampling8Cmd</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[208]"></a>USART_OneBitMethodCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[be]"></a>USART_SendData</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendByte_USART2
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_SendByte
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendString
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendByte
</UL>

<P><STRONG><a name="[cf]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[209]"></a>USART_SetAddress</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20a]"></a>USART_ReceiverWakeUpCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20b]"></a>USART_WakeUpConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20c]"></a>USART_LINBreakDetectLengthConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20d]"></a>USART_LINCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20e]"></a>USART_SendBreak</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[20f]"></a>USART_HalfDuplexCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[210]"></a>USART_SetGuardTime</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[211]"></a>USART_SmartCardCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[212]"></a>USART_SmartCardNACKCmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[213]"></a>USART_IrDAConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[214]"></a>USART_IrDACmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[215]"></a>USART_DMACmd</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[c6]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_Init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[bf]"></a>USART_GetFlagStatus</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendByte_USART2
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_SendByte
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendString
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendByte
</UL>

<P><STRONG><a name="[c8]"></a>USART_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart3_Init
</UL>

<P><STRONG><a name="[ce]"></a>USART_GetITStatus</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[d1]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>delay_init</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, delay.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[216]"></a>delay_us</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(.text), UNUSED)

<P><STRONG><a name="[bb]"></a>delay_xms</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[ba]"></a>delay_ms</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, delay.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_xms
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servor_Proc
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[8c]"></a>Set_Nvic_Irq</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Set_Nvic_Irq &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[bd]"></a>Usart_SendByte</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_SendString
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_SendStr_length
</UL>

<P><STRONG><a name="[c0]"></a>Usart_SendString</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, sys.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>

<P><STRONG><a name="[c1]"></a>Uart3_Init</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, openmvuart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[ca]"></a>Usart3_SendStr_length</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, openmvuart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendByte
</UL>

<P><STRONG><a name="[cb]"></a>Usart3_SendString</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, openmvuart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart_SendByte
</UL>

<P><STRONG><a name="[cc]"></a>Get_Usart3_Data</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, openmvuart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = Get_Usart3_Data &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[d0]"></a>Clear_Uart3_RecBuf</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, openmvuart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[37]"></a>USART3_IRQHandler</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, openmvuart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = USART3_IRQHandler &rArr; Get_Usart3_Data &rArr; __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Uart3_RecBuf
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Usart3_Data
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[85]"></a>_sys_exit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usart2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[68]"></a>fputc</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, usart2.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_file.o(.text)
</UL>
<P><STRONG><a name="[8b]"></a>USART2_Init</STRONG> (Thumb, 166 bytes, Stack size 40 bytes, usart2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USART2_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[d2]"></a>Serial_SendByte_USART2</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendNumber_USART2
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendString_USART2
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendArray_USART2
</UL>

<P><STRONG><a name="[d3]"></a>Serial_SendArray_USART2</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendByte_USART2
</UL>
<BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_vofa
</UL>

<P><STRONG><a name="[d4]"></a>USART_SendString_USART2</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendByte_USART2
</UL>

<P><STRONG><a name="[d6]"></a>Serial_Pow</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendNumber_USART2
</UL>

<P><STRONG><a name="[d5]"></a>Serial_SendNumber_USART2</STRONG> (Thumb, 58 bytes, Stack size 24 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Pow
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendByte_USART2
</UL>

<P><STRONG><a name="[d8]"></a>vofa_To_four</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_vofa
</UL>

<P><STRONG><a name="[d7]"></a>Send_vofa</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vofa_To_four
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_SendArray_USART2
</UL>

<P><STRONG><a name="[217]"></a>vofa_recvdata</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, usart2.o(.text), UNUSED)

<P><STRONG><a name="[dc]"></a>Clear_Uart2_RecBuf</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, usart2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[d9]"></a>Get_Usart2_PData</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[da]"></a>Get_Usart2_IData</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[db]"></a>Get_Usart2_DData</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, usart2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[36]"></a>USART2_IRQHandler</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, usart2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART2_IRQHandler &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Clear_Uart2_RecBuf
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[94]"></a>BaseTimx_Init</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, basetimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = BaseTimx_Init &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[46]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 404 bytes, Stack size 8 bytes, basetimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = TIM6_DAC_IRQHandler &rArr; encoder_update &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_motor_disable
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwm
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_VelocityRing_Control
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f40_41xxx.o(RESET)
</UL>
<P><STRONG><a name="[e6]"></a>HTIM8_GPIO_Config</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HTIM8_GPIO_Config &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_Init
</UL>

<P><STRONG><a name="[e7]"></a>HTim8Mode_Init</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HTim8Mode_Init &rArr; TIM_OC2Init
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8_Init
</UL>

<P><STRONG><a name="[91]"></a>HTim8_Init</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HTim8_Init &rArr; HTim8Mode_Init &rArr; TIM_OC2Init
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim8Mode_Init
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTIM8_GPIO_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[96]"></a>HTim8_SetPwmFreq</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HTim8_SetPwmFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[ee]"></a>HTim8_SetPwmDuty</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, htimx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
</UL>

<P><STRONG><a name="[97]"></a>ServorCtrlAngle</STRONG> (Thumb, 140 bytes, Stack size 48 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = ServorCtrlAngle &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare1
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servor_Proc
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[f5]"></a>ServorCtrlAngleNP</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, htimx.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[9b]"></a>Servor_Proc</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, htimx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = Servor_Proc &rArr; ServorCtrlAngle &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_1
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8e]"></a>KEY_Init</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, key.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = KEY_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[f6]"></a>Key_Read</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, key.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Proc
</UL>

<P><STRONG><a name="[218]"></a>Key2_Long_Proc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, key.o(.text), UNUSED)

<P><STRONG><a name="[5]"></a>Key_Proc</STRONG> (Thumb, 136 bytes, Stack size 4 bytes, key.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Key_Proc
</UL>
<BR>[Calls]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Key_Read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(.data)
</UL>
<P><STRONG><a name="[8d]"></a>LED_Init</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LED_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[f7]"></a>OLED_WR_Byte</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawBMP
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowCHinese
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display_Off
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Display_On
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>

<P><STRONG><a name="[fa]"></a>OLED_Set_Pos</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_Set_Pos &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawBMP
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowCHinese
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[fb]"></a>OLED_Display_On</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[fc]"></a>OLED_Display_Off</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[90]"></a>OLED_Clear</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_Clear &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[fd]"></a>OLED_ShowChar</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[ff]"></a>oled_pow</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = oled_pow
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[fe]"></a>OLED_ShowNum</STRONG> (Thumb, 132 bytes, Stack size 56 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_pow
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
</UL>

<P><STRONG><a name="[100]"></a>OLED_ShowString</STRONG> (Thumb, 54 bytes, Stack size 24 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Oled_Proc
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowFloat
</UL>

<P><STRONG><a name="[101]"></a>OLED_ShowCHinese</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[102]"></a>OLED_DrawBMP</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[8f]"></a>OLED_Init</STRONG> (Thumb, 350 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[103]"></a>OLED_ShowFloat</STRONG> (Thumb, 144 bytes, Stack size 48 bytes, oled.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>

<P><STRONG><a name="[4]"></a>Oled_Proc</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, oled.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = Oled_Proc &rArr; OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>
<BR>[Address Reference Count : 1]<UL><LI> main.o(.data)
</UL>
<P><STRONG><a name="[219]"></a>PID_Param_Init</STRONG> (Thumb, 144 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[123]"></a>LocationRing_PID_Realize</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_Out
</UL>

<P><STRONG><a name="[125]"></a>VelocityRing_PID_Realize</STRONG> (Thumb, 212 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VelocityRing_Out
</UL>

<P><STRONG><a name="[105]"></a>HTim1_Init</STRONG> (Thumb, 394 bytes, Stack size 56 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HTim1_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_config_all
</UL>

<P><STRONG><a name="[10a]"></a>Motor1Pwm</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Motor1Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
</UL>

<P><STRONG><a name="[10b]"></a>Motor2Pwm</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Motor2Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
</UL>

<P><STRONG><a name="[10c]"></a>Motor3Pwm</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Motor3Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare1
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
</UL>

<P><STRONG><a name="[10d]"></a>Motor4Pwm</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Motor4Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
</UL>

<P><STRONG><a name="[10e]"></a>MotorSetPWM</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MotorSetPWM &rArr; Motor4Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor4Pwm
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor3Pwm
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2Pwm
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1Pwm
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwmduty
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwm
</UL>

<P><STRONG><a name="[10f]"></a>Motor1ConfigIO</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Motor1ConfigIO &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_config_all
</UL>

<P><STRONG><a name="[110]"></a>Motor2ConfigIO</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Motor2ConfigIO &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_config_all
</UL>

<P><STRONG><a name="[111]"></a>Motor3ConfigIO</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, tb6612.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[112]"></a>Motor4ConfigIO</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, tb6612.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>

<P><STRONG><a name="[113]"></a>motor_set_dir</STRONG> (Thumb, 280 bytes, Stack size 0 bytes, tb6612.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwmduty
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_pwm
</UL>

<P><STRONG><a name="[e4]"></a>motor_set_pwm</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = motor_set_pwm &rArr; MotorSetPWM &rArr; Motor4Pwm
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_dir
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[114]"></a>motor_set_pwmduty</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, tb6612.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_dir
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MotorSetPWM
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
</UL>

<P><STRONG><a name="[92]"></a>motor_config_all</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, tb6612.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = motor_config_all &rArr; HTim1_Init &rArr; GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor2ConfigIO
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor1ConfigIO
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HTim1_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[128]"></a>set_motor_enable</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, tb6612.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
</UL>

<P><STRONG><a name="[e2]"></a>set_motor_disable</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, tb6612.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[115]"></a>TIM4_MOTOR1_encoder_init</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM4_MOTOR1_encoder_init &rArr; TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICStructInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_config_all
</UL>

<P><STRONG><a name="[11b]"></a>TIM5_MOTOR2_encoder_init</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM5_MOTOR2_encoder_init &rArr; TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICStructInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_config_all
</UL>

<P><STRONG><a name="[11c]"></a>TIM2_MOTOR3_encoder_init</STRONG> (Thumb, 252 bytes, Stack size 40 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM2_MOTOR3_encoder_init &rArr; TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICStructInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_config_all
</UL>

<P><STRONG><a name="[11d]"></a>TIM3_MOTOR4_encoder_init</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = TIM3_MOTOR4_encoder_init &rArr; TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICStructInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>
<BR>[Called By]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_config_all
</UL>

<P><STRONG><a name="[93]"></a>encoder_config_all</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = encoder_config_all &rArr; TIM3_MOTOR4_encoder_init &rArr; TIM_ICInit &rArr; TI3_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_MOTOR4_encoder_init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM2_MOTOR3_encoder_init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM5_MOTOR2_encoder_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_MOTOR1_encoder_init
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;System_Init
</UL>

<P><STRONG><a name="[11e]"></a>read_encoder</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = read_encoder
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
</UL>
<BR>[Called By]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calc_motor_rotate_speed
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
</UL>

<P><STRONG><a name="[21a]"></a>encoder_get_dir</STRONG> (Thumb, 108 bytes, Stack size 0 bytes, encoder.o(.text), UNUSED)

<P><STRONG><a name="[11f]"></a>encoder_get_dir_delta</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = encoder_get_dir_delta
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
</UL>

<P><STRONG><a name="[e1]"></a>encoder_update</STRONG> (Thumb, 142 bytes, Stack size 24 bytes, encoder.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = encoder_update &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_get_dir_delta
<LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_encoder
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[121]"></a>calc_motor_rotate_speed</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, encoder.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;read_encoder
</UL>

<P><STRONG><a name="[122]"></a>LocationRing_Out</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LocationRing_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_PID_Realize
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_VelocityRing_Control
</UL>

<P><STRONG><a name="[124]"></a>VelocityRing_Out</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = VelocityRing_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VelocityRing_PID_Realize
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_VelocityRing_Control
</UL>

<P><STRONG><a name="[e3]"></a>LocationRing_VelocityRing_Control</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = LocationRing_VelocityRing_Control &rArr; VelocityRing_Out
</UL>
<BR>[Calls]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;VelocityRing_Out
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LocationRing_Out
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM6_DAC_IRQHandler
</UL>

<P><STRONG><a name="[9c]"></a>Car_Tracking</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = Car_Tracking &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_motor_enable
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_dir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Task_1
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9d]"></a>Car_Back</STRONG> (Thumb, 134 bytes, Stack size 40 bytes, task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Car_Back &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dneg
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_motor_enable
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;motor_set_dir
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2d
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9a]"></a>Task_1</STRONG> (Thumb, 414 bytes, Stack size 8 bytes, task1.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = Task_1 &rArr; Servor_Proc &rArr; ServorCtrlAngle &rArr; __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Servor_Proc
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[21b]"></a>__use_no_semihosting</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi_2.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>__2printf</STRONG> (Thumb, 20 bytes, Stack size 24 bytes, noretval__2printf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136 + Unknown Stack Size
<LI>Call Chain = __2printf &rArr; _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test
</UL>

<P><STRONG><a name="[104]"></a>__2sprintf</STRONG> (Thumb, 34 bytes, Stack size 32 bytes, noretval__2sprintf.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowFloat
</UL>

<P><STRONG><a name="[73]"></a>_printf_int_dec</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, _printf_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _printf_int_dec &rArr; _printf_int_common
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_d
</UL>

<P><STRONG><a name="[12d]"></a>__printf</STRONG> (Thumb, 270 bytes, Stack size 32 bytes, __printf_wp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32 + Unknown Stack Size
<LI>Call Chain = __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_percent
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>

<P><STRONG><a name="[cd]"></a>__0sscanf</STRONG> (Thumb, 52 bytes, Stack size 72 bytes, __0sscanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 496<LI>Call Chain = __0sscanf &rArr; __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Usart2_DData
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Usart2_IData
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Usart2_PData
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Usart3_Data
</UL>

<P><STRONG><a name="[131]"></a>_scanf_int</STRONG> (Thumb, 332 bytes, Stack size 56 bytes, _scanf_int.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _scanf_int
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[21c]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[21d]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[21e]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[21f]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[220]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[12c]"></a>_printf_int_common</STRONG> (Thumb, 178 bytes, Stack size 32 bytes, _printf_intcommon.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _printf_int_common
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_int_dec
</UL>

<P><STRONG><a name="[221]"></a>__lib_sel_fp_printf</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, _printf_fp_dec.o(.text), UNUSED)

<P><STRONG><a name="[139]"></a>_printf_fp_dec_real</STRONG> (Thumb, 620 bytes, Stack size 104 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_infnan
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec
</UL>

<P><STRONG><a name="[12b]"></a>_printf_char_common</STRONG> (Thumb, 32 bytes, Stack size 64 bytes, _printf_char_common.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96 + Unknown Stack Size
<LI>Call Chain = _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[64]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _sputc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> noretval__2sprintf.o(.text)
</UL>
<P><STRONG><a name="[12a]"></a>_printf_char_file</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, _printf_char_file.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112 + Unknown Stack Size
<LI>Call Chain = _printf_char_file &rArr; _printf_char_common &rArr; __printf
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ferror
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_common
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>

<P><STRONG><a name="[132]"></a>_chval</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, _chval.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_hex_real
</UL>

<P><STRONG><a name="[141]"></a>_scanf_really_real</STRONG> (Thumb, 668 bytes, Stack size 120 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_is_digit
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_infnan
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_hex_real
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>
<BR>[Called By]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>

<P><STRONG><a name="[130]"></a>__vfscanf_char</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = __vfscanf_char &rArr; __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>
<BR>[Called By]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sscanf
</UL>

<P><STRONG><a name="[65]"></a>_sgetc</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[66]"></a>_sbackspace</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, _sgetc.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> __0sscanf.o(.text)
</UL>
<P><STRONG><a name="[77]"></a>__rt_locale</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_locale_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_common
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[13e]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[222]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[223]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[138]"></a>_ll_udiv10</STRONG> (Thumb, 138 bytes, Stack size 12 bytes, lludiv10.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = _ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[6a]"></a>isspace</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, isspace.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = isspace &rArr; __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_ctype_table
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL>
<P><STRONG><a name="[13b]"></a>_printf_fp_infnan</STRONG> (Thumb, 112 bytes, Stack size 24 bytes, _printf_fp_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _printf_fp_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[145]"></a>__vfscanf</STRONG> (Thumb, 878 bytes, Stack size 96 bytes, _scanf.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = __vfscanf &rArr; _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_int
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_real
</UL>
<BR>[Called By]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf_char
</UL>

<P><STRONG><a name="[134]"></a>_btod_etento</STRONG> (Thumb, 216 bytes, Stack size 72 bytes, bigflt0.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[13c]"></a>ferror</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ferror.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_char_file
</UL>

<P><STRONG><a name="[224]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[148]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[225]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[7c]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[146]"></a>__rt_ctype_table</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, rt_ctype_table.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __rt_ctype_table
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_locale
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;isspace
</UL>

<P><STRONG><a name="[14a]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[14b]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
</UL>

<P><STRONG><a name="[14c]"></a>_scanf_really_hex_real</STRONG> (Thumb, 786 bytes, Stack size 80 bytes, scanf_hexfp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = _scanf_really_hex_real &rArr; __support_ldexp &rArr; __hardfp_ldexp &rArr; __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__support_ldexp
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_chval
</UL>
<BR>[Called By]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_hex_real
</UL>

<P><STRONG><a name="[172]"></a>_scanf_really_infnan</STRONG> (Thumb, 292 bytes, Stack size 72 bytes, scanf_infnan.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _scanf_really_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_infnan
</UL>

<P><STRONG><a name="[81]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[15d]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_ctype
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_get_lc_numeric
</UL>

<P><STRONG><a name="[14d]"></a>__aeabi_llsl</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_hex_real
</UL>

<P><STRONG><a name="[226]"></a>_ll_shift_l</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[135]"></a>_btod_d2e</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e))
<BR><BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[151]"></a>_d2e_denorm_low</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_denorm_low))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_norm_op1
</UL>

<P><STRONG><a name="[150]"></a>_d2e_norm_op1</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, btod.o(CL$$btod_d2e_norm_op1))
<BR><BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2e_denorm_low
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
</UL>

<P><STRONG><a name="[154]"></a>__btod_div_common</STRONG> (Thumb, 696 bytes, Stack size 24 bytes, btod.o(CL$$btod_div_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_edivd
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
</UL>

<P><STRONG><a name="[152]"></a>_e2d</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, btod.o(CL$$btod_e2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = _e2d &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emuld
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_edivd
</UL>

<P><STRONG><a name="[153]"></a>_e2e</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, btod.o(CL$$btod_e2e))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = _e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2d
</UL>

<P><STRONG><a name="[136]"></a>_btod_ediv</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_ediv))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _btod_ediv &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[140]"></a>_btod_edivd</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_edivd))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _btod_edivd &rArr; _e2d &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2d
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_div_common
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[137]"></a>_btod_emul</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_emul))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2e
</UL>
<BR>[Called By]<UL><LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[13f]"></a>_btod_emuld</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, btod.o(CL$$btod_emuld))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = _btod_emuld &rArr; _e2d &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__btod_mult_common
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_e2d
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_value
</UL>

<P><STRONG><a name="[155]"></a>__btod_mult_common</STRONG> (Thumb, 580 bytes, Stack size 16 bytes, btod.o(CL$$btod_mult_common))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __btod_mult_common
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emuld
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
</UL>

<P><STRONG><a name="[13a]"></a>__ARM_fpclassify</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __ARM_fpclassify
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[156]"></a>__hardfp_ldexp</STRONG> (Thumb, 200 bytes, Stack size 48 bytes, ldexp.o(i.__hardfp_ldexp))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __hardfp_ldexp &rArr; __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__support_ldexp
</UL>

<P><STRONG><a name="[15a]"></a>__mathlib_dbl_overflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_overflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_overflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
</UL>

<P><STRONG><a name="[159]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
</UL>

<P><STRONG><a name="[144]"></a>__mathlib_narrow</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, narrow.o(i.__mathlib_narrow))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = __mathlib_narrow &rArr; frexp &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frexp
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmpeq
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_hex_real
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[14e]"></a>__support_ldexp</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ldexp.o(i.__support_ldexp))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = __support_ldexp &rArr; __hardfp_ldexp &rArr; __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_hex_real
</UL>

<P><STRONG><a name="[12e]"></a>_is_digit</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, __printf_wp.o(i._is_digit))
<BR><BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__printf
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[15b]"></a>frexp</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, frexp.o(i.frexp))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = frexp &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
</UL>

<P><STRONG><a name="[7a]"></a>_get_lc_numeric</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_numeric_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_numeric
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_numeric_2
</UL>

<P><STRONG><a name="[6b]"></a>_get_lc_ctype</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lc_ctype_c.o(locale$$code))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _get_lc_ctype
</UL>
<BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_lc_ctype_2
</UL>
<BR>[Address Reference Count : 1]<UL><LI> rt_ctype_table.o(.text)
</UL>
<P><STRONG><a name="[129]"></a>__aeabi_dneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic))
<BR><BR>[Called By]<UL><LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
</UL>

<P><STRONG><a name="[227]"></a>_dneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[228]"></a>__aeabi_fneg</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[229]"></a>_fneg</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[22a]"></a>_dabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[22b]"></a>_fabs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, basic.o(x$fpl$basic), UNUSED)

<P><STRONG><a name="[f2]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngleNP
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
</UL>

<P><STRONG><a name="[15e]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[f1]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngleNP
</UL>

<P><STRONG><a name="[161]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
</UL>

<P><STRONG><a name="[164]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[168]"></a>__fpl_dcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, dcmpi.o(x$fpl$dcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
</UL>

<P><STRONG><a name="[127]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
</UL>

<P><STRONG><a name="[166]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[157]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_cdcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
</UL>

<P><STRONG><a name="[167]"></a>_dcmpeq</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, deqf.o(x$fpl$deqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[126]"></a>__aeabi_ui2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
</UL>

<P><STRONG><a name="[22c]"></a>_dfltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, dflt_clz.o(x$fpl$dfltu), UNUSED)

<P><STRONG><a name="[22d]"></a>__aeabi_cdcmple</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)

<P><STRONG><a name="[169]"></a>_dcmple</STRONG> (Thumb, 120 bytes, Stack size 32 bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmp_Inf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[16c]"></a>__fpl_dcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, dleqf.o(x$fpl$dleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drcmple
</UL>

<P><STRONG><a name="[120]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Tracking
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Back
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_overflow
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;frexp
</UL>

<P><STRONG><a name="[16a]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[160]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmple
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dcmpeq
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[163]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[15c]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_narrow
</UL>

<P><STRONG><a name="[16b]"></a>_drcmple</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, drleqf.o(x$fpl$drleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcmple_InfNaN
</UL>

<P><STRONG><a name="[22e]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)

<P><STRONG><a name="[16d]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngle
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;test
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowFloat
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ServorCtrlAngleNP
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_update
</UL>

<P><STRONG><a name="[16f]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[170]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[75]"></a>_fp_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fpinit.o(x$fpl$fpinit))
<BR><BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init_fp_1
</UL>

<P><STRONG><a name="[22f]"></a>__fplib_config_fpu_vfp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[230]"></a>__fplib_config_pureend_doubles</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fpinit.o(x$fpl$fpinit), UNUSED)

<P><STRONG><a name="[15f]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[71]"></a>_printf_fp_dec</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, printf1.o(x$fpl$printf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = _printf_fp_dec &rArr; _printf_fp_dec_real &rArr; _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_f
</UL>

<P><STRONG><a name="[165]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[158]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_ldexp
</UL>

<P><STRONG><a name="[147]"></a>_scanf_real</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, scanf1.o(x$fpl$scanf1))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = _scanf_real &rArr; _scanf_really_real &rArr; _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>
<BR>[Called By]<UL><LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__vfscanf
</UL>

<P><STRONG><a name="[143]"></a>_scanf_hex_real</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, scanf2.o(x$fpl$scanf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = _scanf_hex_real &rArr; _scanf_really_hex_real &rArr; __support_ldexp &rArr; __hardfp_ldexp &rArr; __mathlib_dbl_underflow &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_hex_real
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[142]"></a>_scanf_infnan</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, scanf2.o(x$fpl$scanf2))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = _scanf_infnan &rArr; _scanf_really_infnan
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_infnan
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[171]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9e]"></a>SetSysClock</STRONG> (Thumb, 220 bytes, Stack size 12 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[ad]"></a>TI4_Config</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI4_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[ab]"></a>TI3_Config</STRONG> (Thumb, 72 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI3_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[a9]"></a>TI2_Config</STRONG> (Thumb, 90 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI2_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[a7]"></a>TI1_Config</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI1_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[c9]"></a>Usart3_SendByte</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, openmvuart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
</UL>

<P><STRONG><a name="[16e]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
</UL>

<P><STRONG><a name="[162]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[133]"></a>_fp_digits</STRONG> (Thumb, 428 bytes, Stack size 96 bytes, _printf_fp_dec.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = _fp_digits &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emul
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_ediv
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_d2e
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_udiv10
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_fp_dec_real
</UL>

<P><STRONG><a name="[67]"></a>_printf_input_char</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, _printf_char_common.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> _printf_char_common.o(.text)
</UL>
<P><STRONG><a name="[13d]"></a>_fp_value</STRONG> (Thumb, 508 bytes, Stack size 88 bytes, scanf_fp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = _fp_value &rArr; _btod_etento &rArr; _btod_emul &rArr; _e2e
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_emuld
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_edivd
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_btod_etento
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_scanf_really_real
</UL>

<P><STRONG><a name="[69]"></a>_scanf_char_input</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, scanf_char.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> scanf_char.o(.text)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
